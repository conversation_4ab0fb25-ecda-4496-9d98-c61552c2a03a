import { UtrValueType } from '../../server/models/public/universalTrackerType';
import type { UtrvData, XBRLMapping } from '../../server/service/reporting/types';
import { NumberScale } from '../../server/types/units';
import { createExtendedUtrv } from './compositeUTRVFixtures';
import { utrOneId, utrTwoId } from './universalTrackerFixtures';
import { utrValueOneId, utrValueTwoId } from './universalTrackerValueFixtures';
import type { SerializedElementNode, SerializedLexicalNode, ParagraphNode } from 'lexical';
import type { SerializedHeadingNode, HeadingNode } from '@lexical/rich-text';
import type { SerializedListNode, ListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';
import ContextError from '../../server/error/ContextError';

export const factOne = 'esrs:BasisForPreparationOfSustainabilityStatement'; // Section: "BP-1", dataPointId: "BP-1_01"
export const factTwo = 'esrs:PercentageOfTotalEmissionsOfPollutantsToSoilOccurringInAreasAtWaterRisk'; // Section: "E2-4", dataPointId: "E2-4_13"
export const factThree = 'esrs:GeneralBasisForPreparationOfSustainabilityStatementAbstract'; // No section, dataPointId
export const noRefFact = 'esrs:NoRefFact';

// Types from types.ts
export const xbrlMapping: XBRLMapping = {
  [factOne]: { factName: factOne, utrCode: 'utr1' },
  [factTwo]: { factName: factTwo, utrCode: 'utr2' },
  [factThree]: { factName: factThree, utrCode: 'utr3' },
};

export const utrCodeToUtrvMap: Map<string, UtrvData> = new Map([
  [
    'utr1',
    createExtendedUtrv({
      id: utrValueTwoId,
      utrId: utrOneId,
      value: 123,
      overridesUtr: { code: 'utr1', valueType: UtrValueType.Number, numberScale: NumberScale.Hundreds, unit: 'm3' },
    }),
  ],
  [
    'utr2',
    createExtendedUtrv({
      id: utrValueOneId,
      utrId: utrTwoId,
      overrides: { valueData: { data: 'abc' } },
      overridesUtr: { code: 'utr2', valueType: UtrValueType.Text, numberScale: undefined, unit: undefined },
    }),
  ],
]);

// Types for the lexical state structure
export interface LexicalRootNode extends SerializedElementNode {
  type: 'root';
  children: SerializedLexicalNode[];
}

export interface LexicalHeadingNode extends SerializedHeadingNode {
  children: SerializedLexicalNode[];
}

interface LexicalParagraphNode extends SerializedElementNode {
  type: 'paragraph';
  children: SerializedLexicalNode[];
}

interface LexicalListNode extends SerializedListNode {
  children: SerializedElementNode[];
}

export interface LexicalTextNode extends SerializedLexicalNode {
  type: 'text';
  text: string;
}

// Type guard helpers
function isTextNode(node: SerializedLexicalNode): node is LexicalTextNode {
  return node.type === 'text';
}

function isParagraphNode(node: SerializedLexicalNode): node is LexicalParagraphNode {
  return node.type === 'paragraph';
}

export function isHeadingNode(node: SerializedLexicalNode): node is LexicalHeadingNode {
  return node.type === 'heading';
}

export function isListNode(node: SerializedLexicalNode): node is LexicalListNode {
  return node.type === 'list';
}

// Helper functions to reduce test duplication
export function findParagraphWithText(
  children: SerializedLexicalNode[],
  text: string
): LexicalParagraphNode | undefined {
  return children.find(
    (node): node is LexicalParagraphNode =>
      isParagraphNode(node) && node.children.some((child) => isTextNode(child) && child.text.includes(text))
  );
}

export function findHeadingWithText(
  children: SerializedLexicalNode[],
  text: string,
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
): LexicalHeadingNode | undefined {
  return children.find(
    (node): node is LexicalHeadingNode =>
      isHeadingNode(node) &&
      (!tag || node.tag === tag) &&
      node.children.some((child) => isTextNode(child) && child.text.includes(text))
  );
}

// Type guards for actual Lexical nodes (not serialized)
export function isActualHeadingNode(node: LexicalNode): node is HeadingNode {
  return node.getType() === 'heading';
}

export function isActualParagraphNode(node: LexicalNode): node is ParagraphNode {
  return node.getType() === 'paragraph';
}

export function isActualListNode(node: LexicalNode): node is ListNode {
  return node.getType() === 'list';
}

// Helper functions for actual Lexical nodes
export function getActualHeadingNode(nodes: LexicalNode[], index: number): HeadingNode {
  const node = nodes[index];
  if (!isActualHeadingNode(node)) {
    throw new ContextError(`Expected heading node at index ${index}, got ${node.getType()}`);
  }
  return node;
}

export function getActualParagraphNode(nodes: LexicalNode[], index: number): ParagraphNode {
  const node = nodes[index];
  if (!isActualParagraphNode(node)) {
    throw new ContextError(`Expected paragraph node at index ${index}, got ${node.getType()}`);
  }
  return node;
}

export function getActualListNode(nodes: LexicalNode[], index: number): ListNode {
  const node = nodes[index];
  if (!isActualListNode(node)) {
    throw new ContextError(`Expected list node at index ${index}, got ${node.getType()}`);
  }
  return node;
}
