import { ObjectId } from 'bson';
import {
  type CreateReportDocument,
  type ReportDocumentPlain,
  ReportDocumentStatus,
  ReportDocumentType,
} from '../../server/models/reportDocument';
import { type SerializedEditorState } from 'lexical';
import BackgroundJob, { TaskStatus, TaskType } from '../../server/models/backgroundJob';
import type {
  GenerateReportLexicalStateTask,
  ProcessIXBRLReportSectionTask,
  SetupIXBRLReportTask,
  SectionBreakdown,
  AIReportDocumentJobModel,
  ReportContext,
} from '../../server/service/report-document/types';
import type { ExtendedTagMappingItem } from '../../server/service/reporting/types';

export const getCreateReportDocumentFixture = (): CreateReportDocument => ({
  title: 'Test Report',
  description: 'Test Content',
  type: ReportDocumentType.CSRD,
  initiativeId: new ObjectId(),
  createdBy: new ObjectId(),
});

export const getReportDocumentFixture = (
  id: ObjectId = new ObjectId(),
  initiativeId: ObjectId = new ObjectId()
): ReportDocumentPlain => ({
  _id: id,
  type: ReportDocumentType.CSRD,
  initiativeId: initiativeId,
  title: 'Fixture Report',
  description: 'Fixture Content',
  createdBy: new ObjectId(),
  created: new Date(),
  lastUpdated: new Date(),
  status: ReportDocumentStatus.Completed,
});

export const getEditorStateFixture = (): SerializedEditorState => ({
  root: {
    children: [],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1,
  },
});

export const getReportContextFixture = (): ReportContext => ({
  initiativeName: 'Test Initiative',
  effectiveDate: 'January 1, 2024',
});

export const createGenerateReportLexicalStateTaskFixture = (
  reportId: ObjectId,
  reportType: ReportDocumentType = ReportDocumentType.CSRD,
  reportContext: ReportContext = getReportContextFixture(),
  overrides = {}
): GenerateReportLexicalStateTask => ({
  id: 'task-3',
  name: 'Generate lexical state task',
  type: TaskType.GenerateReportLexicalState,
  status: TaskStatus.Pending,
  data: {
    reportId,
    reportType,
    reportContext,
    ...overrides,
  },
});

export const createSectionBreakdownFixture = (overrides = {}): SectionBreakdown => ({
  introduction: 'Test introduction',
  summary: 'Test summary',
  xbrlTagSections: [
    {
      title: 'Test Title',
      description: 'Test Description',
      keyHighlights: ['Highlight 1'],
    },
  ],
  conclusion: 'Test conclusion',
  ...overrides,
});

export const createProcessSectionTaskFixture = (
  reportId: ObjectId,
  sectionKey: string,
  reportType: ReportDocumentType = ReportDocumentType.CSRD,
  relevantMapping: Record<string, ExtendedTagMappingItem> = {},
  sectionBreakdown?: SectionBreakdown
): ProcessIXBRLReportSectionTask => ({
  id: `section-task-${sectionKey}`,
  name: `Process ${sectionKey} section`,
  type: TaskType.ProcessIXBRLReportSection,
  status: TaskStatus.Completed,
  data: {
    reportId,
    reportType,
    sectionKey,
    relevantMapping,
    ...(sectionBreakdown && { sectionBreakdown }),
  },
});

export const createMockJobFixture = (
  initiativeId: ObjectId,
  tasks: (GenerateReportLexicalStateTask | ProcessIXBRLReportSectionTask | SetupIXBRLReportTask)[]
): AIReportDocumentJobModel =>
  new BackgroundJob({
    _id: new ObjectId(),
    initiativeId,
    type: 'AIReportDocument',
    name: 'Generating Report Document',
    tasks,
  }) as unknown as AIReportDocumentJobModel;
