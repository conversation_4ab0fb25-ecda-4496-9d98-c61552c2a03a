import { expect } from 'chai';
import sinon from 'sinon';
import { createHeadlessEditor } from '@lexical/headless';
import { editorConfig } from '../../../../server/service/reporting/utils';
import { buildStaticSection } from '../../../../server/service/reporting/sections/staticBuilder';
import type { StaticSection } from '../../../../server/service/reporting/sections/utils';
import {
  getActualHeadingNode,
  getActualParagraphNode,
  getActualListNode
} from '../../../fixtures/xbrlReportFixtures';

describe('staticBuilder', () => {
  const sandbox = sinon.createSandbox();
  let editor: ReturnType<typeof createHeadlessEditor>;

  beforeEach(() => {
    editor = createHeadlessEditor(editorConfig);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('buildStaticSection', () => {
    const mockSection: StaticSection = {
      heading: 'Test Section',
      description: 'This is a test section description',
      subsections: [
        {
          heading: 'Subsection 1',
          description: 'Description 1'
        },
        {
          heading: 'Subsection 2',
          description: 'Description 2'
        }
      ]
    };

    it('should create proper heading node with section title', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        expect(result).to.have.length.greaterThan(0);
        
        // First node should be the heading
        const headingNode = getActualHeadingNode(result, 0);
        expect(headingNode.getType()).to.equal('heading');
      });
    });

    it('should create description paragraph node', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        expect(result).to.have.length.greaterThan(1);
        
        // Second node should be the description paragraph
        const descriptionNode = getActualParagraphNode(result, 1);
        expect(descriptionNode.getType()).to.equal('paragraph');
      });
    });

    it('should create numbered list with subsection items when subsections exist', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        expect(result).to.have.length(3); // heading, description, list
        
        // Third node should be the list
        const listNode = getActualListNode(result, 2);
        expect(listNode.getType()).to.equal('list');
      });
    });

    it('should handle section with no subsections', () => {
      const sectionWithoutSubsections: StaticSection = {
        heading: 'Empty Section',
        description: 'Section with no subsections',
        subsections: []
      };

      editor.update(() => {
        const result = buildStaticSection({ section: sectionWithoutSubsections });
        
        expect(result).to.have.length(2); // Only heading and description, no list
        
        const headingNode = getActualHeadingNode(result, 0);
        const descriptionNode = getActualParagraphNode(result, 1);
        
        expect(headingNode.getType()).to.equal('heading');
        expect(descriptionNode.getType()).to.equal('paragraph');
      });
    });

    it('should include emoji in heading text', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        const headingNode = getActualHeadingNode(result, 0);
        expect(headingNode.getType()).to.equal('heading');
        
        // The heading should contain the emoji and title
        // Note: Since we can't easily inspect the text content without Lexical context,
        // we'll verify the structure is correct
        expect(headingNode.getChildrenSize()).to.be.greaterThan(0);
      });
    });

    it('should create list items for each subsection', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        const listNode = getActualListNode(result, 2);
        expect(listNode.getType()).to.equal('list');
        
        // List should have 2 items for the 2 subsections
        expect(listNode.getChildrenSize()).to.equal(2);
      });
    });

    it('should handle single subsection', () => {
      const singleSubsectionSection: StaticSection = {
        heading: 'Single Sub Section',
        description: 'Section with one subsection',
        subsections: [
          {
            heading: 'Only Subsection',
            description: 'The only subsection description'
          }
        ]
      };

      editor.update(() => {
        const result = buildStaticSection({ section: singleSubsectionSection });
        
        expect(result).to.have.length(3); // heading, description, list
        
        const listNode = getActualListNode(result, 2);
        expect(listNode.getType()).to.equal('list');
        expect(listNode.getChildrenSize()).to.equal(1);
      });
    });

    it('should maintain correct node order', () => {
      editor.update(() => {
        const result = buildStaticSection({ section: mockSection });
        
        const headingNode = getActualHeadingNode(result, 0);
        const paragraphNode = getActualParagraphNode(result, 1);
        const listNode = getActualListNode(result, 2);
        
        expect(headingNode.getType()).to.equal('heading');
        expect(paragraphNode.getType()).to.equal('paragraph');
        expect(listNode.getType()).to.equal('list');
      });
    });
  });
});