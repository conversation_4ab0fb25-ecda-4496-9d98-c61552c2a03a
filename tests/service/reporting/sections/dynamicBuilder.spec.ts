import { expect } from 'chai';
import sinon from 'sinon';
import { createHeadlessEditor } from '@lexical/headless';
import { editorConfig } from '../../../../server/service/reporting/utils';
import { buildDynamicSection } from '../../../../server/service/reporting/sections/dynamicBuilder';
import type { DynamicSection } from '../../../../server/service/reporting/sections/utils';
import type { SectionData } from '../../../../server/service/reporting/xhtml/types';
import { type XbrlTracker } from '../../../../server/service/reporting/XbrlTracker';
import type { UtrvData, XBRLMapping, TagMappingItem } from '../../../../server/service/reporting/types';
import { ObjectId } from 'bson';
import { createStubbedTestDouble, type StubbedInstance } from '../../../utils/test-doubles';
import { universalTrackerOne } from '../../../fixtures/universalTrackerFixtures';

describe('dynamicBuilder', () => {
  const sandbox = sinon.createSandbox();
  let mockTracker: StubbedInstance<XbrlTracker>;
  let editor: ReturnType<typeof createHeadlessEditor>;

  beforeEach(() => {
    editor = createHeadlessEditor(editorConfig);
    mockTracker = createStubbedTestDouble<XbrlTracker>({
      getContextId: sandbox.stub().returns('c-1'),
      getFactId: sandbox.stub().returns('fact-1')
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('buildDynamicSection', () => {
    const mockSection: DynamicSection = {
      heading: 'Dynamic Test Section',
      description: 'This is a dynamic test section description',
      relatedTags: ['tag1', 'tag2'],
      subsections: [
        {
          heading: 'Subsection 1',
          description: 'Subsection 1 description',
          keyDisclosures: ['Disclosure 1', 'Disclosure 2'],
          relatedTags: ['subsection_tag1', 'subsection_tag2']
        },
        {
          heading: 'Subsection 2',
          description: 'Subsection 2 description',
          keyDisclosures: [],
          relatedTags: ['subsection_tag3']
        }
      ]
    };

    const mockUtrvData: UtrvData = {
      _id: new ObjectId(),
      value: 100,
      valueData: {},
      status: 'verified',
      effectiveDate: new Date(),
      universalTracker: universalTrackerOne,
    };

    const mockTagMapping: TagMappingItem = {
      factName: 'subsection_tag1',
      utrCode: 'TEST_001'
    };

    const mockMapping: XBRLMapping = {
      'subsection_tag1': mockTagMapping,
      'subsection_tag2': {
        factName: 'subsection_tag2',
        utrCode: 'TEST_002'
      }
    };

    const mockUtrCodeToUtrvMap = new Map<string, UtrvData>([
      ['TEST_001', mockUtrvData]
    ]);

    const mockSectionData: SectionData = {
      initiative: {
        _id: new ObjectId(),
        name: 'Test Initiative'
      },
      mapping: mockMapping,
      utrCodeToUtrvMap: mockUtrCodeToUtrvMap,
      tracker: mockTracker as unknown as XbrlTracker
    };

    it('should create proper section structure', () => {
      editor.update(() => {
        const result = buildDynamicSection({ sectionData: mockSectionData, section: mockSection });
        
        expect(result).to.have.length.greaterThan(0);
        
        // First node should be section heading
        const sectionHeadingNode = result[0];
        expect(sectionHeadingNode.getType()).to.equal('heading');
      });
    });

    it('should include section description', () => {
      editor.update(() => {
        const result = buildDynamicSection({ sectionData: mockSectionData, section: mockSection });
        
        expect(result).to.have.length.greaterThan(1);
        
        // Second node should be description
        const descriptionNode = result[1];
        expect(descriptionNode.getType()).to.equal('paragraph');
      });
    });

    it('should create IXBRL nodes for related tags', () => {
      editor.update(() => {
        const result = buildDynamicSection({ sectionData: mockSectionData, section: mockSection });
        
        // Should contain IXBRL nodes for the related tags
        expect(result.length).to.be.greaterThan(2);
        
        // Check that tracker methods were called for IXBRL node creation
        expect(mockTracker.getContextId.called).to.be.true;
        expect(mockTracker.getFactId.called).to.be.true;
      });
    });

    it('should handle section with empty related tags', () => {
      const sectionWithoutTags: DynamicSection = {
        ...mockSection,
        relatedTags: []
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: mockSectionData, 
          section: sectionWithoutTags 
        });
        
        expect(result).to.have.length.greaterThan(0);
        
        // Should still have heading and description
        expect(result[0].getType()).to.equal('heading');
        expect(result[1].getType()).to.equal('paragraph');
      });
    });

    it('should process subsections correctly', () => {
      editor.update(() => {
        const result = buildDynamicSection({ sectionData: mockSectionData, section: mockSection });
        
        // Should have nodes for both subsections
        expect(result.length).to.be.greaterThan(4); // heading, description, related tags, subsections
      });
    });

    it('should handle subsection without key disclosures', () => {
      const sectionWithEmptyDisclosures: DynamicSection = {
        ...mockSection,
        subsections: [
          {
            heading: 'Empty Subsection',
            description: 'Subsection with no disclosures',
            keyDisclosures: [],
            relatedTags: []
          }
        ]
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: mockSectionData, 
          section: sectionWithEmptyDisclosures 
        });
        
        expect(result).to.have.length.greaterThan(0);
      });
    });

    it('should handle subsection without related tags', () => {
      const sectionWithoutSubTags: DynamicSection = {
        ...mockSection,
        subsections: [
          {
            heading: 'No Tags Subsection',
            description: 'Subsection with no related tags',
            keyDisclosures: ['Some disclosure'],
            relatedTags: []
          }
        ]
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: mockSectionData, 
          section: sectionWithoutSubTags 
        });
        
        expect(result).to.have.length.greaterThan(0);
      });
    });

    it('should handle empty mapping', () => {
      const sectionDataWithEmptyMapping: SectionData = {
        ...mockSectionData,
        mapping: {}
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: sectionDataWithEmptyMapping, 
          section: mockSection 
        });
        
        expect(result).to.have.length.greaterThan(0);
      });
    });

    it('should handle empty utrCodeToUtrvMap', () => {
      const sectionDataWithEmptyMap: SectionData = {
        ...mockSectionData,
        utrCodeToUtrvMap: new Map<string, UtrvData>()
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: sectionDataWithEmptyMap, 
          section: mockSection 
        });
        
        expect(result).to.have.length.greaterThan(0);
      });
    });

    it('should maintain proper node ordering', () => {
      editor.update(() => {
        const result = buildDynamicSection({ sectionData: mockSectionData, section: mockSection });
        
        // First node should be heading
        expect(result[0].getType()).to.equal('heading');
        
        // Second node should be description
        expect(result[1].getType()).to.equal('paragraph');
      });
    });

    it('should handle single subsection', () => {
      const singleSubsectionSection: DynamicSection = {
        ...mockSection,
        subsections: [mockSection.subsections[0]]
      };

      editor.update(() => {
        const result = buildDynamicSection({ 
          sectionData: mockSectionData, 
          section: singleSubsectionSection 
        });
        
        expect(result).to.have.length.greaterThan(0);
      });
    });
  });
});