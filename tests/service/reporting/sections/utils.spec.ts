import { expect } from 'chai';
import { isDynamicSection } from '../../../../server/service/reporting/sections/utils';
import type { StaticSection, DynamicSection, OutlineSection } from '../../../../server/service/reporting/sections/utils';

describe('sections/utils', () => {
  describe('isDynamicSection', () => {
    it('should return true for dynamic section', () => {
      const dynamicSection: DynamicSection = {
        heading: 'Dynamic Section',
        description: 'A dynamic section with related tags',
        relatedTags: ['tag1', 'tag2'],
        subsections: [
          {
            heading: 'Subsection',
            description: 'Subsection description',
            keyDisclosures: ['disclosure1'],
            relatedTags: ['subTag1']
          }
        ]
      };

      const result = isDynamicSection(dynamicSection);
      expect(result).to.be.true;
    });

    it('should return false for static section', () => {
      const staticSection: StaticSection = {
        heading: 'Static Section',
        description: 'A static section without related tags',
        subsections: [
          {
            heading: 'Subsection',
            description: 'Subsection description'
          }
        ]
      };

      const result = isDynamicSection(staticSection);
      expect(result).to.be.false;
    });

    it('should return true for dynamic section with empty related tags', () => {
      const dynamicSectionWithEmptyTags: DynamicSection = {
        heading: 'Dynamic Section',
        description: 'A dynamic section with empty related tags',
        relatedTags: [],
        subsections: []
      };

      const result = isDynamicSection(dynamicSectionWithEmptyTags);
      expect(result).to.be.true;
    });

    it('should return false for static section with empty subsections', () => {
      const staticSectionWithEmptySubsections: StaticSection = {
        heading: 'Static Section',
        description: 'A static section with empty subsections',
        subsections: []
      };

      const result = isDynamicSection(staticSectionWithEmptySubsections);
      expect(result).to.be.false;
    });

    it('should work with outline section type', () => {
      const dynamicOutlineSection: OutlineSection = {
        heading: 'Outline Dynamic Section',
        description: 'An outline section that is dynamic',
        relatedTags: ['outline_tag'],
        subsections: [
          {
            heading: 'Outline Subsection',
            description: 'Outline subsection description',
            keyDisclosures: [],
            relatedTags: []
          }
        ]
      } as DynamicSection;

      const result = isDynamicSection(dynamicOutlineSection);
      expect(result).to.be.true;
    });

    it('should work with outline section type for static section', () => {
      const staticOutlineSection: OutlineSection = {
        heading: 'Outline Static Section',
        description: 'An outline section that is static',
        subsections: [
          {
            heading: 'Outline Subsection',
            description: 'Outline subsection description'
          }
        ]
      } as StaticSection;

      const result = isDynamicSection(staticOutlineSection);
      expect(result).to.be.false;
    });

    it('should handle dynamic section with complex subsections', () => {
      const complexDynamicSection: DynamicSection = {
        heading: 'Complex Dynamic Section',
        description: 'A complex dynamic section',
        relatedTags: ['complex_tag1', 'complex_tag2'],
        subsections: [
          {
            heading: 'Complex Subsection 1',
            description: 'First complex subsection',
            keyDisclosures: ['disclosure1', 'disclosure2', 'disclosure3'],
            relatedTags: ['sub_tag1', 'sub_tag2']
          },
          {
            heading: 'Complex Subsection 2',
            description: 'Second complex subsection',
            keyDisclosures: ['disclosure4'],
            relatedTags: ['sub_tag3']
          }
        ]
      };

      const result = isDynamicSection(complexDynamicSection);
      expect(result).to.be.true;
    });

    it('should handle static section with multiple subsections', () => {
      const multipleSubsectionsStaticSection: StaticSection = {
        heading: 'Multiple Subsections Static',
        description: 'Static section with multiple subsections',
        subsections: [
          {
            heading: 'Static Subsection 1',
            description: 'First static subsection'
          },
          {
            heading: 'Static Subsection 2',
            description: 'Second static subsection'
          },
          {
            heading: 'Static Subsection 3',
            description: 'Third static subsection'
          }
        ]
      };

      const result = isDynamicSection(multipleSubsectionsStaticSection);
      expect(result).to.be.false;
    });
  });
});