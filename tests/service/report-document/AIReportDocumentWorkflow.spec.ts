/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { expect } from 'chai';
import { createSandbox, type SinonStub } from 'sinon';
import { ObjectId } from 'bson';
import {
  AIReportDocumentWorkflow,
  getAIReportDocumentWorkflow,
} from '../../../server/service/report-document/AIReportDocumentWorkflow';
import {
  JobType,
  TaskType,
  JobStatus,
  TaskStatus,
  type BackgroundJobModel,
} from '../../../server/models/backgroundJob';
import ReportDocument, { ReportDocumentStatus, ReportDocumentType } from '../../../server/models/reportDocument';
import ContextError from '../../../server/error/ContextError';
import { testLogger } from '../../factories/logger';
import type {
  AIReportDocumentJobModel,
  AIReportDocumentTask,
  ProcessIXBRLReportSectionTask,
  SetupIXBRLReportTask,
} from '../../../server/service/report-document/types';
import { type ReportDocumentManager } from '../../../server/service/report-document/ReportDocumentManager';
import { createJobModel, surveyId } from '../../fixtures/backgroundJobFixtures';
import { createMockWithStubs, type MockWithStubs } from '../../utils/test-doubles';
import Survey from '../../../server/models/survey';
import UtrExternalMapping from '../../../server/models/utrExternalMapping';
import UniversalTrackerValue from '../../../server/models/universalTrackerValue';
import { type AIBreakdownBuilder } from '../../../server/service/reporting/sections/AIBreakdownBuilder';
import { type UnifiedAIModelFactory } from '../../../server/service/ai/UnifiedAIModelFactory';
import { createMongooseModel } from '../../setup';
import * as reportingUtils from '../../../server/service/reporting/utils';
import { universalTrackerOne } from '../../fixtures/universalTrackerFixtures';
import { utrValueOneId, utrValueTwoId } from '../../fixtures/universalTrackerValueFixtures';
import { excludeSoftDeleted } from '../../../server/repository/aggregations';
import { universalTrackerLookup } from '../../../server/repository/utrvAggregations';
import { ActionList } from '../../../server/types/constants';
import type { FileSupportAiModelWithCapabilities } from '../../../server/service/ai/models/FileSupportAiModel';
import {
  getReportContextFixture,
  createGenerateReportLexicalStateTaskFixture,
  createSectionBreakdownFixture,
  createProcessSectionTaskFixture,
  createMockJobFixture,
  getEditorStateFixture,
} from '../../fixtures/reportDocumentFixtures';
import type { UtrvData } from '../../../server/service/reporting/types';
import type { DynamicSection } from '../../../server/service/reporting/sections/utils';

// Test interfaces for accessing protected members
interface WorkflowProtectedMethods {
  startTask(job: AIReportDocumentJobModel, task: AIReportDocumentTask): Promise<void>;
  completeTask(job: AIReportDocumentJobModel, task: AIReportDocumentTask): Promise<AIReportDocumentJobModel>;
  failTask(job: AIReportDocumentJobModel, task: AIReportDocumentTask, error: Error): Promise<AIReportDocumentJobModel>;
  jobType: JobType;
}

describe('AIReportDocumentWorkflow', () => {
  const sandbox = createSandbox();

  let workflow: AIReportDocumentWorkflow;
  let mockReportDocumentManager: MockWithStubs<ReportDocumentManager>;
  let mockSurveyModel: typeof Survey;
  let mockUtrExternalMappingModel: typeof UtrExternalMapping;
  let mockUniversalTrackerValueModel: typeof UniversalTrackerValue;
  let mockAiBreakdownBuilder: MockWithStubs<AIBreakdownBuilder>;
  let mockUnifiedModelFactory: MockWithStubs<UnifiedAIModelFactory>;
  let mockAIModel: MockWithStubs<FileSupportAiModelWithCapabilities>;

  let reportDocumentFindByIdStub: SinonStub;
  let surveyFindStub: SinonStub;
  let utrExternalMappingFindStub: SinonStub;
  let utrvAggregateStub: SinonStub;
  let utrvFindStub: SinonStub;
  let startTaskStub: SinonStub;
  let completeTaskStub: SinonStub;
  let failTaskStub: SinonStub;

  const reportId = new ObjectId();
  const jobId = new ObjectId();
  const initiativeId = new ObjectId();
  const reportContext = {
    initiativeName: 'Test Initiative',
    effectiveDate: '2024-01-01',
  };

  const mockUtrvData: UtrvData[] = [
    {
      _id: utrValueOneId,
      value: 100,
      valueData: {},
      status: ActionList.Verified,
      effectiveDate: new Date(),
      universalTracker: universalTrackerOne,
    },
  ];

  beforeEach(() => {
    // Create mock dependencies
    mockReportDocumentManager = createMockWithStubs<ReportDocumentManager>({
      updateReportStatus: sandbox.stub().resolves(),
    });

    mockAiBreakdownBuilder = createMockWithStubs<AIBreakdownBuilder>({
      buildLexicalState: sandbox.stub().resolves(getEditorStateFixture()),
    });

    mockAIModel = createMockWithStubs<FileSupportAiModelWithCapabilities>({
      executeWithFiles: sandbox.stub(),
    });

    mockUnifiedModelFactory = createMockWithStubs<UnifiedAIModelFactory>({
      getFileSupportModel: sandbox.stub().returns(mockAIModel.mock),
    });

    mockSurveyModel = Survey;
    mockUtrExternalMappingModel = UtrExternalMapping;
    mockUniversalTrackerValueModel = UniversalTrackerValue;

    // Set up stubs for Mongoose models
    reportDocumentFindByIdStub = sandbox.stub(ReportDocument, 'findById');
    surveyFindStub = sandbox.stub(Survey, 'find');
    utrExternalMappingFindStub = sandbox.stub(UtrExternalMapping, 'find');
    utrvAggregateStub = sandbox.stub(UniversalTrackerValue, 'aggregate');
    utrvFindStub = sandbox.stub(UniversalTrackerValue, 'find');

    // Create the workflow instance with mocked dependencies
    workflow = new AIReportDocumentWorkflow(
      testLogger,
      mockReportDocumentManager.mock,
      mockSurveyModel,
      mockUtrExternalMappingModel,
      mockUniversalTrackerValueModel,
      mockAiBreakdownBuilder.mock,
      mockUnifiedModelFactory.mock
    );

    // Stub the parent class protected methods using proper interface
    const workflowProtected = workflow as AIReportDocumentWorkflow & WorkflowProtectedMethods;
    startTaskStub = sandbox.stub(workflowProtected, 'startTask').resolves();
    completeTaskStub = sandbox.stub(workflowProtected, 'completeTask').resolves({
      _id: jobId,
      status: JobStatus.Processing,
    } as AIReportDocumentJobModel);
    failTaskStub = sandbox.stub(workflowProtected, 'failTask').resolves({
      _id: jobId,
      status: JobStatus.Error,
    } as AIReportDocumentJobModel);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('isAIReportDocumentJob', () => {
    it('should return true for AI report document job', () => {
      const job: BackgroundJobModel = createJobModel({
        _id: jobId,
        type: JobType.AIReportDocument,
        status: JobStatus.Processing,
        tasks: [],
      });

      expect(workflow.isAIReportDocumentJob(job)).to.be.true;
    });

    it('should return false for non-AI report document job', () => {
      const job: BackgroundJobModel = createJobModel({
        _id: jobId,
        type: JobType.BulkSurveyCreate,
        status: JobStatus.Processing,
        tasks: [],
      });

      expect(workflow.isAIReportDocumentJob(job)).to.be.false;
    });
  });

  describe('processTask', () => {
    let mockJob: AIReportDocumentJobModel;

    beforeEach(() => {
      mockJob = createJobModel({
        _id: jobId,
        type: JobType.AIReportDocument,
        status: JobStatus.Processing,
        tasks: [],
        initiativeId: new ObjectId(),
        userId: new ObjectId(),
        name: 'Test Job',
        created: new Date(),
        logs: [],
      }) as unknown as AIReportDocumentJobModel;
    });


    describe('GenerateReportLexicalState task', () => {
      it('should process report lexical state task successfully', async () => {
        const task: AIReportDocumentTask = {
          id: 'task-3',
          name: 'Generate Report Lexical State',
          type: TaskType.GenerateReportLexicalState,
          status: TaskStatus.Pending,
          data: {
            reportId,
            reportType: ReportDocumentType.CSRD,
            reportContext,
          },
        };

        const result = await workflow.processTask(mockJob, task);

        expect(startTaskStub.calledOnceWith(mockJob, task)).to.be.true;
        expect(
          mockReportDocumentManager.stubs.updateReportStatus.calledOnceWith({
            reportId,
            status: ReportDocumentStatus.Generated,
          })
        ).to.be.true;
        expect(completeTaskStub.calledOnceWith(mockJob, task)).to.be.true;
        expect(result).to.deep.include({
          executeNextTask: true,
        });
        expect(result.job).to.exist;
      });

      it('should handle errors in lexical state generation task', async () => {
        const task: AIReportDocumentTask = {
          id: 'task-3',
          name: 'Generate Report Lexical State',
          type: TaskType.GenerateReportLexicalState,
          status: TaskStatus.Pending,
          data: {
            reportId,
            reportType: ReportDocumentType.CSRD,
            reportContext,
          },
        };

        const error = new ContextError('Lexical state generation failed', { taskId: task.id });
        const processStub = sandbox.stub(workflow, 'processReportLexicalStateTask').rejects(error);

        const result = await workflow.processTask(mockJob, task);

        expect(startTaskStub.calledOnceWith(mockJob, task)).to.be.true;
        expect(
          mockReportDocumentManager.stubs.updateReportStatus.calledOnceWith({
            reportId,
            status: ReportDocumentStatus.Generated,
          })
        ).to.be.true;
        expect(failTaskStub.calledOnceWith(mockJob, task, error)).to.be.true;
        expect(result).to.deep.include({
          executeNextTask: true,
        });
        expect(result.job).to.exist;
        expect(mockJob.status).to.equal(JobStatus.Error);
      });
    });

    describe('unknown task type', () => {
      it('should handle unknown task type with ContextError', async () => {
        const task = {
          id: 'task-unknown',
          name: 'Unknown Task',
          type: 'UnknownTaskType' as never,
          status: TaskStatus.Pending,
          data: {
            reportId,
            reportType: ReportDocumentType.CSRD,
          },
        } as AIReportDocumentTask;

        const result = await workflow.processTask(mockJob, task);

        expect(startTaskStub.calledOnceWith(mockJob, task)).to.be.true;
        expect(failTaskStub.calledOnce).to.be.true;

        const failTaskArgs = failTaskStub.firstCall.args;
        expect(failTaskArgs[0]).to.equal(mockJob);
        expect(failTaskArgs[1]).to.equal(task);
        expect(failTaskArgs[2]).to.be.instanceOf(ContextError);
        expect(failTaskArgs[2].message).to.include(
          `Found not handled job ${jobId} task type ${JobType.AIReportDocument}`
        );

        expect(result).to.deep.include({
          executeNextTask: true,
        });
        expect(result.job).to.exist;
      });
    });

    describe('error handling for report status update', () => {
      it('should handle report status update failure in error scenarios', async () => {
        const task: AIReportDocumentTask = {
          id: 'task-1',
          name: 'Setup IXBRL Report',
          type: TaskType.SetupIXBRLReport,
          status: TaskStatus.Pending,
          data: {
            reportId,
            reportType: ReportDocumentType.CSRD,
          },
        };

        const setupError = new ContextError('Setup task failed', { taskId: task.id });

        sandbox.stub(workflow, 'processSetupTask').rejects(setupError);
        mockReportDocumentManager.stubs.updateReportStatus.resolves();

        const result = await workflow.processTask(mockJob, task);

        expect(
          mockReportDocumentManager.stubs.updateReportStatus.calledOnceWith({
            reportId,
            status: ReportDocumentStatus.Generated,
          })
        ).to.be.true;
        expect(failTaskStub.calledOnceWith(mockJob, task, setupError)).to.be.true;
        expect(result).to.deep.include({
          executeNextTask: true,
        });
        expect(mockJob.status).to.equal(JobStatus.Error);
      });
    });
  });

  describe('singleton instance', () => {
    it('should return the same instance', () => {
      const instance1 = getAIReportDocumentWorkflow();
      const instance2 = getAIReportDocumentWorkflow();

      expect(instance1).to.exist;
      expect(instance1).to.equal(instance2);
    });

    it('should create instance with correct dependencies', () => {
      const instance = getAIReportDocumentWorkflow();

      expect(instance).to.be.instanceOf(AIReportDocumentWorkflow);
      const instanceProtected = instance as AIReportDocumentWorkflow & WorkflowProtectedMethods;
      expect(instanceProtected.jobType).to.equal(JobType.AIReportDocument);
    });
  });


  describe('processSetupTask', () => {
    it('should process setup task and create section tasks', async () => {
      const mockTask: SetupIXBRLReportTask = {
        id: 'task-1',
        name: 'Setup task',
        type: TaskType.SetupIXBRLReport,
        status: TaskStatus.Pending,
        data: {
          reportType: ReportDocumentType.CSRD,
          reportId,
        },
      };

      const mockJob = {
        _id: new ObjectId(),
        initiativeId,
        type: JobType.AIReportDocument,
        name: 'Generating Report Document',
        tasks: [mockTask],
      } as unknown as AIReportDocumentJobModel;

      const mockSurvey = {
        _id: surveyId,
        initiativeId,
        visibleUtrvs: [utrValueOneId, utrValueTwoId],
        initiative: { _id: initiativeId, name: 'Test Initiative' },
      };

      reportDocumentFindByIdStub.returns(createMongooseModel({ surveyIds: [surveyId] }));
      surveyFindStub.returns(createMongooseModel([mockSurvey]));
      utrExternalMappingFindStub.returns({
        lean: sandbox.stub().resolves([]),
      });

      const mockAggregate = {
        exec: sandbox.stub().resolves(mockUtrvData),
      };
      utrvAggregateStub.returns(mockAggregate);

      // Mock the utility functions
      sandbox.stub(reportingUtils, 'getMappingsByType').returns({
        'esrs:WaterConsumption': { factName: 'esrs:WaterConsumption', utrCode: universalTrackerOne.code },
      });

      sandbox.stub(reportingUtils, 'getReportOutline').returns({
        section1: {
          heading: 'Test Section',
          description: 'Test Description',
          subsections: [
            {
              heading: 'Subsection 1',
              description: 'Subsection 1 Description',
              keyDisclosures: ['Key Disclosure 1'],
              relatedTags: ['esrs:WaterConsumption'],
            },
          ],
          relatedTags: [],
        },
      });

      await workflow.processSetupTask(mockJob, mockTask);

      expect(surveyFindStub.calledOnce).to.be.true;
      expect(utrExternalMappingFindStub.calledOnce).to.be.true;
      expect(
        utrvAggregateStub.calledOnceWith(
          sandbox.match([
            {
              $match: {
                _id: { $in: [utrValueOneId, utrValueTwoId] },
                ...excludeSoftDeleted(),
              },
            },
            universalTrackerLookup,
            {
              $match: {
                'universalTracker.code': { $in: [universalTrackerOne.code] },
              },
            },
            {
              $project: {
                _id: 1,
                universalTracker: {
                  $arrayElemAt: ['$universalTracker', 0],
                },
              },
            },
          ])
        )
      ).to.be.true;
      expect(mockJob.tasks.length).to.equal(3);
    });
  });

  describe('generateReportSection', () => {
    it('should generate report section using AI model', async () => {
      const mockSection: DynamicSection = {
        heading: 'Test Section',
        description: 'Test Description',
        subsections: [
          {
            heading: 'Subsection 1',
            description: 'Subsection 1 Description',
            keyDisclosures: ['Key Disclosure 1'],
            relatedTags: ['esrs:WaterConsumption'],
          },
        ],
        relatedTags: [],
      };

      const mockResponse = {
        data: {
          introduction: 'Test introduction',
          summary: 'Test summary',
          xbrlTagSections: [
            {
              title: 'Test Title',
              description: 'Test Description',
              keyHighlights: ['Highlight 1', 'Highlight 2'],
            },
          ],
          conclusion: 'Test conclusion',
        },
        tokenUsage: { inputTokens: 100, outputTokens: 50 },
      };

      mockAIModel.stubs.executeWithFiles.resolves(mockResponse);

      const mockRelevantMapping = {
        'esrs:WaterConsumption': {
          factName: 'esrs:WaterConsumption',
          utrCode: 'TEST001',
          valueListCode: 'VL001',
          utrvId: new ObjectId(),
        },
      };

      utrvFindStub.returns(createMongooseModel(mockUtrvData));

      const result = await workflow.generateReportSection({
        reportId,
        reportType: ReportDocumentType.CSRD,
        section: mockSection,
        relevantMapping: mockRelevantMapping,
        modelName: 'gemini-2.5-flash',
      });

      expect(result).to.deep.equal(mockResponse.data);
      expect(mockAIModel.stubs.executeWithFiles.calledOnce).to.be.true;
    });
  });

  describe('processIXBRLReportSectionTask', () => {
    it('should process IXBRL report section task', async () => {
      const mockTask: ProcessIXBRLReportSectionTask = {
        id: 'task-2',
        name: 'Process section task',
        type: TaskType.ProcessIXBRLReportSection,
        status: TaskStatus.Pending,
        data: {
          reportId,
          reportType: ReportDocumentType.CSRD,
          sectionKey: 'strategy',
          relevantMapping: {
            'test:FactName': {
              factName: 'test:FactName',
              utrCode: 'TEST001',
              valueListCode: 'VL001',
              utrvId: new ObjectId(),
            },
          },
        },
      };

      const mockJob = {
        _id: new ObjectId(),
        initiativeId,
        type: JobType.AIReportDocument,
        name: 'Generating Report Document',
        tasks: [mockTask],
      } as unknown as AIReportDocumentJobModel;

      const mockSectionBreakdown = {
        introduction: 'Test introduction',
        summary: 'Test summary',
        xbrlTagSections: [
          {
            title: 'Test Title',
            description: 'Test Description',
            keyHighlights: ['Highlight 1'],
          },
        ],
        conclusion: 'Test conclusion',
      };

      // Mock getReportOutline to return a section for the strategy key
      sandbox.stub(reportingUtils, 'getReportOutline').returns({
        strategy: {
          heading: '3. Strategy',
          description: 'Strategy description',
          relatedTags: [],
          subsections: [],
        } as DynamicSection,
      });

      sandbox.stub(workflow, 'generateReportSection').resolves(mockSectionBreakdown);

      await workflow.processIXBRLReportSectionTask(mockJob, mockTask);

      expect(mockTask.data.sectionBreakdown).to.deep.equal(mockSectionBreakdown);
    });
  });

  describe('processReportLexicalStateTask', () => {
    const mockReportContext = getReportContextFixture();
    const mockLexicalState = getEditorStateFixture();

    it('should generate lexical state from section breakdowns', async () => {
      const mockTask = createGenerateReportLexicalStateTaskFixture(reportId);
      const mockSectionBreakdown = createSectionBreakdownFixture();
      const mockSectionTask = createProcessSectionTaskFixture(
        reportId,
        'strategy',
        ReportDocumentType.CSRD,
        {
          'test:FactName': {
            factName: 'test:FactName',
            utrCode: 'TEST001',
            valueListCode: 'VL001',
            utrvId: utrValueOneId,
          },
        },
        mockSectionBreakdown
      );

      const mockJob = createMockJobFixture(initiativeId, [mockTask, mockSectionTask]);

      utrvFindStub.returns(createMongooseModel(mockUtrvData));

      await workflow.processReportLexicalStateTask(mockJob, mockTask);

      expect(mockAiBreakdownBuilder.stubs.buildLexicalState.calledOnce).to.be.true;
      expect(mockAiBreakdownBuilder.stubs.buildLexicalState.firstCall.args[0]).to.deep.include({
        reportId,
        reportType: ReportDocumentType.CSRD,
        reportContext: mockReportContext,
      });

      const sectionsDataArg = mockAiBreakdownBuilder.stubs.buildLexicalState.firstCall.args[0].sectionsData;
      expect(sectionsDataArg).to.have.property('strategy');
      expect(sectionsDataArg.strategy).to.deep.include({
        relevantMapping: mockSectionTask.data.relevantMapping,
        breakdown: mockSectionBreakdown,
        utrvData: mockUtrvData,
      });

      expect(mockTask.data.lexicalState).to.deep.equal(mockLexicalState);
    });

    it('should handle multiple section tasks', async () => {
      const mockTask = createGenerateReportLexicalStateTaskFixture(reportId);

      const mockSectionBreakdown1 = createSectionBreakdownFixture({
        introduction: 'Strategy introduction',
        summary: 'Strategy summary',
        xbrlTagSections: [],
        conclusion: 'Strategy conclusion',
      });

      const mockSectionBreakdown2 = createSectionBreakdownFixture({
        introduction: 'Governance introduction',
        summary: 'Governance summary',
        xbrlTagSections: [],
        conclusion: 'Governance conclusion',
      });

      const mockSectionTask1 = createProcessSectionTaskFixture(
        reportId,
        'strategy',
        ReportDocumentType.CSRD,
        {
          'test:FactName1': {
            factName: 'test:FactName1',
            utrCode: 'TEST001',
            valueListCode: 'VL001',
            utrvId: utrValueOneId,
          },
        },
        mockSectionBreakdown1
      );

      const mockSectionTask2 = createProcessSectionTaskFixture(
        reportId,
        'governance',
        ReportDocumentType.CSRD,
        {
          'test:FactName2': {
            factName: 'test:FactName2',
            utrCode: 'TEST002',
            valueListCode: 'VL002',
            utrvId: utrValueTwoId,
          },
        },
        mockSectionBreakdown2
      );

      const mockJob = createMockJobFixture(initiativeId, [mockTask, mockSectionTask1, mockSectionTask2]);

      utrvFindStub.onFirstCall().returns(
        createMongooseModel([
          {
            _id: utrValueOneId,
            value: 100,
            valueData: {},
            status: ActionList.Verified,
            effectiveDate: new Date(),
            universalTracker: universalTrackerOne,
          },
        ])
      );

      utrvFindStub.onSecondCall().returns(
        createMongooseModel([
          {
            _id: utrValueTwoId,
            value: 200,
            valueData: {},
            status: ActionList.Verified,
            effectiveDate: new Date(),
            universalTracker: universalTrackerOne,
          },
        ])
      );

      await workflow.processReportLexicalStateTask(mockJob, mockTask);

      expect(mockAiBreakdownBuilder.stubs.buildLexicalState.calledOnce).to.be.true;

      const sectionsDataArg = mockAiBreakdownBuilder.stubs.buildLexicalState.firstCall.args[0].sectionsData;
      expect(sectionsDataArg).to.have.property('strategy');
      expect(sectionsDataArg).to.have.property('governance');
      expect(Object.keys(sectionsDataArg)).to.have.lengthOf(2);

      expect(sectionsDataArg.strategy?.breakdown).to.deep.equal(mockSectionBreakdown1);
      expect(sectionsDataArg.governance?.breakdown).to.deep.equal(mockSectionBreakdown2);

      expect(mockTask.data.lexicalState).to.deep.equal(mockLexicalState);
    });

    it('should skip tasks without section breakdown', async () => {
      const mockTask = createGenerateReportLexicalStateTaskFixture(reportId);

      const mockSectionTaskWithoutBreakdown = createProcessSectionTaskFixture(reportId, 'strategy');

      const mockOtherTask: SetupIXBRLReportTask = {
        id: 'other-task',
        name: 'Other task',
        type: TaskType.SetupIXBRLReport,
        status: TaskStatus.Completed,
        data: {
          reportId,
          reportType: ReportDocumentType.CSRD,
        },
      };

      const mockJob = createMockJobFixture(initiativeId, [mockTask, mockSectionTaskWithoutBreakdown, mockOtherTask]);

      await workflow.processReportLexicalStateTask(mockJob, mockTask);

      expect(mockAiBreakdownBuilder.stubs.buildLexicalState.calledOnce).to.be.true;

      const sectionsDataArg = mockAiBreakdownBuilder.stubs.buildLexicalState.firstCall.args[0].sectionsData;
      expect(sectionsDataArg).to.be.an('object');
      expect(Object.keys(sectionsDataArg)).to.have.lengthOf(0);

      expect(mockTask.data.lexicalState).to.deep.equal(mockLexicalState);
    });

    it('should handle empty relevant mapping', async () => {
      const mockTask = createGenerateReportLexicalStateTaskFixture(reportId);

      const mockSectionBreakdown = createSectionBreakdownFixture({
        xbrlTagSections: [],
      });

      const mockSectionTask = createProcessSectionTaskFixture(
        reportId,
        'strategy',
        ReportDocumentType.CSRD,
        {},
        mockSectionBreakdown
      );

      const mockJob = createMockJobFixture(initiativeId, [mockTask, mockSectionTask]);

      // Mock empty UTRV data since relevant mapping is empty
      utrvFindStub.returns(createMongooseModel([]));

      await workflow.processReportLexicalStateTask(mockJob, mockTask);

      expect(mockAiBreakdownBuilder.stubs.buildLexicalState.calledOnce).to.be.true;

      const sectionsDataArg = mockAiBreakdownBuilder.stubs.buildLexicalState.firstCall.args[0].sectionsData;
      expect(sectionsDataArg).to.have.property('strategy');
      expect(sectionsDataArg.strategy?.utrvData).to.deep.equal([]);

      expect(mockTask.data.lexicalState).to.deep.equal(mockLexicalState);
    });
  });
});
