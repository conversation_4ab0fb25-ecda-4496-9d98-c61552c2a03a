/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { createHeadlessEditor } from '@lexical/headless';
import { $getRoot, type SerializedEditorState } from 'lexical';
import { ReportDocumentType, type ReportDocumentPlain } from '../../../models/reportDocument';
import type { LoggerInterface } from '../../wwgLogger';
import { wwgLogger } from '../../wwgLogger';
import type { SectionData } from '../xhtml/types';
import { XbrlTracker } from '../XbrlTracker';
import type { GenerateLexicalStateParams } from '../types';
import { editorConfig } from '../utils';
import { LexicalStateGenerator } from '../lexical/LexicalStateGenerator';
import { getReportGenerator } from '../xhtml/ReportGenerator';
import { buildStaticSection } from '../sections/staticBuilder';
import { ISSB_REPORT_OUTLINE } from './outline';
import { buildDynamicSection } from '../sections/dynamicBuilder';
import type { DynamicSection, StaticSection } from '../sections/utils';

export class IssbLexicalStateGenerator extends LexicalStateGenerator {
  constructor(private logger: LoggerInterface, reportGenerator: ReturnType<typeof getReportGenerator>) {
    super(ReportDocumentType.ISSB, reportGenerator);
  }

  public async generateTemplateLexicalState(params: GenerateLexicalStateParams) {
    const { initiative, survey, mapping, utrCodeToUtrvMap } = params;

    this.logger.info(`Generating Lexical state for survey: ${survey._id}`, {
      surveyId: survey._id,
      initiativeId: survey.initiativeId,
    });

    const tracker = new XbrlTracker();
    const sectionData: SectionData = {
      initiative,
      mapping,
      utrCodeToUtrvMap,
      tracker,
    };

    // Use Lexical headless editor to build the state
    const editor = createHeadlessEditor(editorConfig);
    editor.update(
      () => {
        $getRoot().append(
          ...buildStaticSection({ section: ISSB_REPORT_OUTLINE.tableOfContents as StaticSection }),
          ...buildDynamicSection({ sectionData, section: ISSB_REPORT_OUTLINE.requirementsAndFoundation as DynamicSection }),
          ...buildDynamicSection({ sectionData, section: ISSB_REPORT_OUTLINE.governance as DynamicSection }),
          ...buildDynamicSection({ sectionData, section: ISSB_REPORT_OUTLINE.strategy as DynamicSection }),
          ...buildDynamicSection({ sectionData, section: ISSB_REPORT_OUTLINE.riskManagement as DynamicSection }),
          ...buildDynamicSection({ sectionData, section: ISSB_REPORT_OUTLINE.metricsAndTargets as DynamicSection })
        );
      },
      { discrete: true }
    );

    return editor.getEditorState().toJSON();
  }

  public async downloadReport({
    reportDocument,
    editorState,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
  }) {
    // Preview is false as the debug scripts is not ready for issb
    return super.downloadReport({ reportDocument, editorState, preview: false });
  }
}

let instance: IssbLexicalStateGenerator;
export const getIssbLexicalStateGenerator = () => {
  if (!instance) {
    instance = new IssbLexicalStateGenerator(wwgLogger, getReportGenerator());
  }
  return instance;
};
