import { $createHeadingNode } from '@lexical/rich-text';
import { $createParagraphNode, $createTextNode } from 'lexical';
import { $createListItemNode, $createListNode } from '@lexical/list';
import type { LexicalNode } from 'lexical/LexicalNode';
import type { StaticSection } from './utils';

export function buildStaticSection({ section }: { section: StaticSection }): LexicalNode[] {
  const nodes = [];
  const heading = $createHeadingNode('h2');
  heading.append($createTextNode(`📑 ${section.heading}`));
  nodes.push(heading);

  const overviewParagraph = $createParagraphNode();
  overviewParagraph.append($createTextNode(section.description));
  nodes.push(overviewParagraph);

  const list = $createListNode('number');
  const items = section.subsections.map(({ heading }) => heading);
  items.forEach((itemText) => {
    const listItem = $createListItemNode();
    const paragraph = $createParagraphNode();
    const subHeading = $createHeadingNode('h4');
    subHeading.append($createTextNode(itemText));
    paragraph.append(subHeading);
    listItem.append(paragraph);
    list.append(listItem);
  });

  if (items.length) {
    nodes.push(list);
  }

  return nodes;
}
