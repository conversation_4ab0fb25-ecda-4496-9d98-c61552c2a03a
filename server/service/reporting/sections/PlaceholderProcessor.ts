import { $createTextNode } from 'lexical';
import type { LexicalNode } from 'lexical/LexicalNode';
import type { ExtendedTagMappingItem, XBRLMapping, UtrvData } from '../types';
import type { XbrlTracker } from '../XbrlTracker';
import { createXBRLNode } from '../lexical/utils';
import { commonPlaceholders } from '../../report-document/prompts';
import type { ReportContext } from '../../report-document/types';

const PLACEHOLDER_REGEX = /\{\{\{[^}]+\}\}\}/g;
const PLACEHOLDER_VALIDATION_REGEX = /^\{\{\{.+\}\}\}$/;
const PLACEHOLDER_EXTRACTION_REGEX = /^\{\{\{|\}\}\}$/g;

export class PlaceholderProcessor {
  /**
   * Processes text with placeholders, creating appropriate nodes for each chunk
   * First splits text into chunks, then processes each chunk:
   * - If chunk is a common placeholder ({{{initiativeName}}}, {{{effectiveDate}}}), creates text node
   * - If chunk is an XBRL tag placeholder, creates XBRL node using UTRV data
   * - Otherwise creates regular text node
   */
  public processTextWithPlaceholders(
    text: string,
    relevantMapping: XBRLMapping<ExtendedTagMappingItem>,
    utrCodeToUtrvMap: Map<string, UtrvData>,
    tracker: XbrlTracker,
    reportContext: ReportContext
  ): LexicalNode[] {
    const chunks = this.splitTextIntoChunks(text);

    const nodes: LexicalNode[] = [];

    chunks.forEach((chunk) => {
      if (!this.isPlaceholder(chunk)) {
        // Only add non-empty text chunks, but don't trim to preserve spacing
        if (chunk.trim()) {
          nodes.push($createTextNode(chunk));
        }
        return;
      }

      const placeholderName = this.extractPlaceholderName(chunk);
      if (this.isCommonPlaceholder(placeholderName)) {
        // Handle common placeholders like {{{initiativeName}}}, {{{effectiveDate}}}
        const value = this.getCommonPlaceholderValue(placeholderName, reportContext);
        nodes.push($createTextNode(value));
        return;
      }

      if (this.isXBRLTag(placeholderName, relevantMapping)) {
        // Handle XBRL tag placeholders - create XBRL node with proper formatting
        const xbrlNode = this.createXBRLNodeForTag(placeholderName, relevantMapping, utrCodeToUtrvMap, tracker);
        if (xbrlNode) {
          nodes.push(xbrlNode);
          return;
        }
        // Fallback to text if XBRL node creation fails
        nodes.push($createTextNode(chunk));
        return;
      }

      // Unknown placeholder, keep as text
      nodes.push($createTextNode(chunk));
    });

    return nodes;
  }

  /**
   * Splits text into chunks, separating placeholders from regular text
   * Example: 'Hello {{{name}}} world' -> ['Hello ', '{{{name}}}', ' world']
   */
  public splitTextIntoChunks(text: string): string[] {
    const placeholderRegex = PLACEHOLDER_REGEX;
    const chunks: string[] = [];
    let lastIndex = 0;
    let match;

    while ((match = placeholderRegex.exec(text)) !== null) {
      // Add text before the placeholder
      if (match.index > lastIndex) {
        chunks.push(text.slice(lastIndex, match.index));
      }

      // Add the placeholder
      chunks.push(match[0]);
      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after the last placeholder
    if (lastIndex < text.length) {
      chunks.push(text.slice(lastIndex));
    }

    return chunks.filter((chunk) => chunk.length > 0); // Remove empty chunks
  }

  /**
   * Checks if a chunk is a placeholder
   */
  public isPlaceholder(chunk: string): boolean {
    return PLACEHOLDER_VALIDATION_REGEX.test(chunk);
  }

  /**
   * Extracts the placeholder name from a placeholder chunk
   */
  public extractPlaceholderName(placeholder: string): string {
    return placeholder.replace(PLACEHOLDER_EXTRACTION_REGEX, '');
  }

  /**
   * Checks if a placeholder is a common placeholder (not an XBRL tag)
   */
  public isCommonPlaceholder(placeholderName: string): boolean {
    return commonPlaceholders.map((p) => p.key).includes(placeholderName);
  }

  /**
   * Gets the value for a common placeholder using report context data
   */
  public getCommonPlaceholderValue(placeholderName: string, reportContext: ReportContext): string {
    if (placeholderName in reportContext) {
      return reportContext[placeholderName as keyof ReportContext];
    }
    return placeholderName;
  }

  /**
   * Checks if a placeholder name corresponds to an XBRL tag
   */
  public isXBRLTag(placeholderName: string, relevantMapping: XBRLMapping<ExtendedTagMappingItem>): boolean {
    return Object.keys(relevantMapping).includes(placeholderName);
  }

  /**
   * Creates an XBRL node for a given tag placeholder
   * Similar to buildXBRLNodes but for individual placeholders
   * Handles both numeric and non-numeric value types with appropriate formatting
   */
  createXBRLNodeForTag(
    factName: string,
    relevantMapping: XBRLMapping<ExtendedTagMappingItem>,
    utrCodeToUtrvMap: Map<string, UtrvData>,
    tracker: XbrlTracker
  ): LexicalNode | null {
    const mappingItem = relevantMapping[factName];
    if (!mappingItem) {
      return null;
    }
    return createXBRLNode({ item: mappingItem, mapping: relevantMapping, utrCodeToUtrvMap, tracker });
  }
}

// Singleton instance
let instance: PlaceholderProcessor | undefined;

export const getPlaceholderProcessor = (): PlaceholderProcessor => {
  if (!instance) {
    instance = new PlaceholderProcessor();
  }
  return instance;
};
