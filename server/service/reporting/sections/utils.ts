interface BaseSection {
  heading: string;
  description: string;
}

export interface StaticSection extends BaseSection {
  subsections: {
    heading: string;
    description: string;
  }[];
}

export interface DynamicSection extends BaseSection {
  relatedTags: string[];
  subsections: {
    heading: string;
    description: string;
    keyDisclosures: string[];
    relatedTags: string[];
  }[];
}

export type OutlineSection = StaticSection | DynamicSection;

export const isDynamicSection = (section: OutlineSection): section is DynamicSection => {
  return 'relatedTags' in section;
};
