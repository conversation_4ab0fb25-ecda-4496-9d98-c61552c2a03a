import { $createHeadingNode } from '@lexical/rich-text';
import { $createLineBreakNode, $createParagraphNode, $createTextNode, type LexicalNode } from 'lexical';
import { buildXBRLNodes, createListNode } from '../lexical/utils';
import type { DynamicSection } from './utils';
import type { SectionData } from '../xhtml/types';
import { getFactLabel, getStringData } from '../utils';
import type { TagMappingItem } from '../types';
import { $createIxbrlNode } from '../lexical/nodes/IXBRLNode';

export function buildDynamicSection({
  sectionData,
  section,
}: {
  sectionData: SectionData;
  section: DynamicSection;
}): LexicalNode[] {
  const { mapping, utrCodeToUtrvMap, tracker } = sectionData;

  const nodes = [];
  const sectionHeading = $createHeadingNode('h2');
  sectionHeading.append($createTextNode(`🏢 ${section.heading}`));
  nodes.push(sectionHeading);

  const sectionDescription = $createParagraphNode();
  sectionDescription.append($createTextNode(section.description));
  nodes.push(sectionDescription);

  const relatedIxbrlNodes = section.relatedTags.map((factName) => {
    const ixbrlNode = $createIxbrlNode({
      tag: 'ix:nonNumeric',
      name: factName,
      contextRef: tracker.getContextId(),
      factId: tracker.getFactId(),
    });

    ixbrlNode.append(
      $createTextNode(
        getStringData({
          factName,
          utrCodeToUtrvMap,
          mapping,
        })
      )
    );

    return ixbrlNode;
  });

  nodes.push(...relatedIxbrlNodes);

  const sectionNodes = [...section.subsections].flatMap(({ heading, description, keyDisclosures, relatedTags }) => {
    const nodes = [];
    const subsectionHeading = $createHeadingNode('h3');
    subsectionHeading.append($createTextNode(heading));
    nodes.push(subsectionHeading);

    const subsectionDescription = $createParagraphNode();
    subsectionDescription.append($createTextNode(description));
    nodes.push(subsectionDescription);

    if (keyDisclosures.length) {
      nodes.push(createListNode(keyDisclosures));
    }

    const relatedIxbrlNodes = relatedTags
      ? buildXBRLNodes({
          items: Object.values(mapping).filter(
            (item): item is TagMappingItem => !!item && relatedTags.includes(item.factName)
          ),
          mapping,
          utrCodeToUtrvMap,
          tracker,
        }).map((node) => {
          const paragraph = $createParagraphNode();
          paragraph.append(
            $createTextNode(`${getFactLabel(node.factName)}`),
            $createLineBreakNode(),
            $createTextNode('Value:  '),
            node.ixbrlNode
          );
          return paragraph;
        })
      : [];

    nodes.push(...relatedIxbrlNodes);

    return nodes;
  });

  nodes.push(...sectionNodes);

  return nodes;
}
