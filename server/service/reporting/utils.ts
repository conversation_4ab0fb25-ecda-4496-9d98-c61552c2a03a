import { type CreateEditorArgs, ParagraphNode } from 'lexical';
import { wwgLogger } from '../wwgLogger';
import { ListItemNode, ListNode } from '@lexical/list';
import { HeadingNode } from '@lexical/rich-text';
import { IXBRLNode } from './lexical/nodes/IXBRLNode';
import { ReportDocumentType } from '../../models/reportDocument';
import { getDefaultESRSMappingList } from './csrd/CsrdMappingList';
import { getDefaultIFRSMappingList } from './issb/IssbMappingList';
import type { UtrvData, XBRLMapping } from './types';
import { CSRDDefinitions } from './csrd/CsrdDefinitions';
import { ISSBDefinitions } from './issb/IssbDefinitions';
import { UtrValueType } from '../../models/public/universalTrackerType';
import { MarkNode } from '@lexical/mark';
import { ISSB_REPORT_OUTLINE } from './issb/outline';
import { CSRD_REPORT_OUTLINE } from './csrd/outline';

export const editorConfig: CreateEditorArgs = {
  onError: (error: Error) => {
    wwgLogger.error(error);
  },
  theme: {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      underline: 'editor-text-underline',
    },
    ixbrlNode: 'ixbrl-plugin-node',
  },
  namespace: 'ReportEditor',
  nodes: [ListNode, ListItemNode, HeadingNode, IXBRLNode, ParagraphNode, MarkNode],
};

const defaultMappings = {
  [ReportDocumentType.CSRD]: getDefaultESRSMappingList(),
  [ReportDocumentType.ISSB]: getDefaultIFRSMappingList(),
};

export const getMappingsByType = ({
  type,
  overrides = {},
}: {
  type: ReportDocumentType;
  overrides?: XBRLMapping;
}): XBRLMapping => {
  return defaultMappings[type].reduce((acc, item) => {
    if (!acc[item.factName]) {
      acc[item.factName] = {
        factName: item.factName,
        utrCode: item.utrCode,
        valueListCode: item.valueListCode,
      };
    }
    return acc;
  }, overrides);
};

export const getTagDefinition = (tag: string) => {
  return { ...CSRDDefinitions, ...ISSBDefinitions }[tag];
};

export const getReportOutline = (reportType: ReportDocumentType) => {
  switch (reportType) {
    case ReportDocumentType.CSRD:
      return CSRD_REPORT_OUTLINE;
    case ReportDocumentType.ISSB:
      return ISSB_REPORT_OUTLINE;
  }
};

type GetDataParams = {
  factName: string;
  mapping: XBRLMapping;
  utrCodeToUtrvMap: Map<string, UtrvData>;
  fallback?: string;
};

export function getData({ factName, mapping, utrCodeToUtrvMap, fallback = '' }: GetDataParams): string | number {
  const item = mapping[factName];
  if (!item) return fallback;
  const utrv = utrCodeToUtrvMap.get(item.utrCode);
  if (!utrv) return fallback;

  switch (utrv.universalTracker.valueType) {
    case UtrValueType.Number:
    case UtrValueType.Sample:
    case UtrValueType.Percentage:
      return utrv.value ?? fallback;
    case UtrValueType.Text:
    case UtrValueType.Date:
      return utrv.valueData?.data ?? fallback;
    case UtrValueType.Table: {
      if (!item.valueListCode) {
        return fallback;
      }
      const firstRow = utrv.valueData?.table?.[0];
      const value = firstRow?.find((col) => col.code === item.valueListCode)?.value;
      return Array.isArray(value) ? value.join(', ') : value ?? fallback;
    }
    case UtrValueType.NumericValueList:
    case UtrValueType.TextValueList:
      if (!item.valueListCode) {
        return fallback;
      }
      return utrv.valueData?.data?.[item.valueListCode] ?? fallback;
    default:
      return fallback;
  }
}

export const getStringData = (params: GetDataParams) => {
  return String(
    getData({
      ...params,
      fallback: params.fallback ?? '-',
    })
  );
};

export const getFactLabel = (tag: string): string => {
  const def = getTagDefinition(tag);
  return def?.label ?? '';
};
