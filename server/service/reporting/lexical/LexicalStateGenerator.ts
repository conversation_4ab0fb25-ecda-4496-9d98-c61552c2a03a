/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import type { ObjectId } from 'bson';
import { createHeadlessEditor } from '@lexical/headless';
import { $generateHtmlFromNodes } from '@lexical/html';
import { type SerializedEditorState } from 'lexical';
import { JSDOM } from 'jsdom';
import { excludeSoftDeleted } from '../../../repository/aggregations';
import { universalTrackerLookup } from '../../../repository/utrvAggregations';
import type { ReportDocumentPlain, ReportDocumentType } from '../../../models/reportDocument';
import type { SurveyModelPlain } from '../../../models/survey';
import Survey from '../../../models/survey';
import type { UserPlain } from '../../../models/user';
import UniversalTrackerValue from '../../../models/universalTrackerValue';
import UtrExternalMapping from '../../../models/utrExternalMapping';
import UserError from '../../../error/UserError';
import type { ReportSection } from '../xhtml/types';
import type { getReportGenerator } from '../xhtml/ReportGenerator';
import type { GenerateLexicalStateParams, ILexicalStateGenerator, UtrvData, XBRLMapping } from '../types';
import { editorConfig, getMappingsByType } from '../utils';

export abstract class LexicalStateGenerator implements ILexicalStateGenerator {
  protected readonly reportType: ReportDocumentType;

  constructor(reportType: ReportDocumentType, protected reportGenerator: ReturnType<typeof getReportGenerator>) {
    this.reportType = reportType;
  }

  abstract generateTemplateLexicalState(params: GenerateLexicalStateParams): Promise<SerializedEditorState>;

  private async getUtrvData(survey: SurveyModelPlain) {
    const aggregations = [
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $unwind: '$universalTracker',
      },
      {
        $project: {
          _id: 1,
          value: 1,
          valueData: 1,
          status: 1,
          effectiveDate: 1,
          universalTracker: ,
        },
      },
    ];
    return UniversalTrackerValue.aggregate<UtrvData>(aggregations).exec();
  }

  private async getGeneratorParameters({ initiativeId, surveyIds }: { initiativeId: ObjectId, surveyIds?: ObjectId[] }) {
    const surveys = await Survey.find({
      ...(surveyIds ? { _id: { $in: surveyIds } } : {}),
      initiativeId,
      deletedDate: { $exists: false },
    })
      .populate('initiative')
      .sort({ effectiveDate: -1, created: -1 })
      .lean<SurveyModelPlain[]>()
      .exec();

    /**
     * Currently use information of 1 survey
     * @todo: Make the report aware of multiple surveys
     */
    if (surveys.length === 0) {
      throw new UserError('No survey found for the initiative associated with this report document.');
    }
    const survey = surveys[0];

    if (!survey.initiative) {
      throw new UserError('Unable to fetch details of selected survey. If this continues please contact our support.');
    }

    const externalMapping = await UtrExternalMapping.find({ type: this.reportType }).lean();
    const externalXbrlMapping = externalMapping.reduce((acc, item) => {
      if (!acc[item.mappingCode]) {
        acc[item.mappingCode] = {
          factName: item.mappingCode,
          utrCode: item.utrs[0].utrCode,
          valueListCode: item.utrs[0].valueListCode,
        };
      }
      return acc;
    }, {} as XBRLMapping);

    // Merge default mapping with external mapping
    const mapping = getMappingsByType({ type: this.reportType, overrides: externalXbrlMapping });

    const utrvData = await this.getUtrvData(survey);
    const utrCodeToUtrvMap = new Map<string, UtrvData>(utrvData.map((utrv) => [utrv.universalTracker.code, utrv]));

    return { initiative: survey.initiative, survey, mapping, utrvData, utrCodeToUtrvMap };
  }

  public async getTemplate({
    initiativeId,
    user,
    surveyIds,
  }: {
    initiativeId: ObjectId;
    user: UserPlain;
    surveyIds?: ObjectId[];
  }): Promise<SerializedEditorState> {
    const generatorParams = await this.getGeneratorParameters({ initiativeId, surveyIds });
    const state = await this.generateTemplateLexicalState({ ...generatorParams, user });
    return state;
  }

  /**
   * Groups a list of DOM nodes into structured sections based on heading elements.
   * @param nodes The list of child nodes from the document body.
   * @returns An array of structured ReportSection objects.
   */
  private parseAndGroupNodes(nodes: NodeListOf<ChildNode>): ReportSection[] {
    const sections: ReportSection[] = [];
    let currentSection: ReportSection | null = null;

    nodes.forEach((node) => {
      const isHeading = node.nodeType === node.ELEMENT_NODE && /^H[1-6]$/.test(node.nodeName);

      if (isHeading) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = {
          header: { title: node.textContent || '' },
          children: [],
        };
      } else {
        if (!currentSection) {
          currentSection = {
            header: { title: 'Introduction' },
            children: [],
          };
        }
        const rawHtml = (node as Element).outerHTML || node.textContent || '';
        // Normalize the HTML to ensure it's valid XHTML.
        const content = rawHtml.replace(/<br>/g, '<br />');

        if (content.trim()) {
          currentSection.children.push({ type: 'html', content });
        }
      }
    });

    if (currentSection) {
      sections.push(currentSection);
    }

    return sections;
  }

  private parseReportSections(editorState: SerializedEditorState): ReportSection[] {
    const dom = new JSDOM();
    global.window = dom.window as unknown as Window & typeof globalThis;
    global.document = dom.window.document;

    const editor = createHeadlessEditor(editorConfig);
    const parsedEditorState = editor.parseEditorState(editorState);
    editor.setEditorState(parsedEditorState);

    let sections: ReportSection[] = [];

    editor.update(() => {
      const html = $generateHtmlFromNodes(editor);
      const reportDom = new JSDOM(html);
      const body = reportDom.window.document.body;
      sections = this.parseAndGroupNodes(body.childNodes);
    });

    return sections;
  }

  public async downloadReport({
    reportDocument,
    editorState,
    preview,
  }: {
    reportDocument: ReportDocumentPlain;
    editorState: SerializedEditorState;
    preview: boolean;
  }) {
    const params = await this.getGeneratorParameters({ initiativeId: reportDocument.initiativeId, surveyIds: reportDocument.surveyIds });
    const sections = this.parseReportSections(editorState);
    return this.reportGenerator.generate({ ...params, reportType: this.reportType, sections, preview });
  }
}
