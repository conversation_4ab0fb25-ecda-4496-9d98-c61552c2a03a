import { type ObjectId } from 'bson';
import ContextError from '../../error/ContextError';
import { JobType, TaskStatus, TaskType, type BackgroundJobModel } from '../../models/backgroundJob';
import ReportDocument, { ReportDocumentStatus, type ReportDocumentType } from '../../models/reportDocument';
import type { SurveyModelPlainWithInitiative } from '../../models/survey';
import Survey from '../../models/survey';
import { JobStatus } from '../../models/surveyTemplateHistory';
import UniversalTrackerValue from '../../models/universalTrackerValue';
import UtrExternalMapping from '../../models/utrExternalMapping';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { BackgroundBaseWorkflow, type TaskResult } from '../background-process/BackgroundBaseWorkflow';
import { getAIBreakdownBuilder, type AIBreakdownBuilder } from '../reporting/sections/AIBreakdownBuilder';
import type { ExtendedTagMappingItem, UtrvData, XBRLMapping } from '../reporting/types';
import { getMappingsByType, getReportOutline } from '../reporting/utils';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import { getReportDocumentManager, type ReportDocumentManager } from './ReportDocumentManager';
import type {
  AIReportDocumentJobModel,
  AIReportDocumentTask,
  GenerateReportLexicalStateTask,
  ProcessIXBRLReportSectionTask,
  SectionBreakdown,
  SectionData,
  SetupIXBRLReportTask,
} from './types';
import { universalTrackerLookup } from '../../repository/utrvAggregations';
import type { UniversalTrackerValueExtended } from '../../models/universalTrackerValue';
import { isDynamicSection, type DynamicSection } from '../reporting/sections/utils';
import { generatedUUID } from '../crypto/token';
import { customDateFormat, DateFormat } from '../../util/date';
import { getUnifiedAIModelFactory, type UnifiedModelName } from '../ai/UnifiedAIModelFactory';
import { z } from 'zod';
import { REPORT_OUTLINE_PROMPT } from './prompts';

const sectionBreakdownSchema = z.object({
  introduction: z.string(),
  summary: z.string(),
  xbrlTagSections: z.array(
    z.object({
      title: z.string(),
      description: z.string(),
      keyHighlights: z.array(z.string()),
    })
  ),
  conclusion: z.string(),
});

export class AIReportDocumentWorkflow extends BackgroundBaseWorkflow<AIReportDocumentJobModel> {
  protected jobType = JobType.AIReportDocument;

  constructor(
    protected logger: LoggerInterface,
    private reportDocumentManager: ReportDocumentManager,
    private surveyModel: typeof Survey,
    private utrExternalMappingModel: typeof UtrExternalMapping,
    private universalTrackerValueModel: typeof UniversalTrackerValue,
    private aiBreakdownBuilder: AIBreakdownBuilder,
    private unifiedModelFactory: ReturnType<typeof getUnifiedAIModelFactory>
  ) {
    super();
  }

  public isAIReportDocumentJob(job: BackgroundJobModel): job is AIReportDocumentJobModel {
    return job.type === this.jobType;
  }

  private async getUtrvsDataMin({ visibleUtrvs, utrCodes }: { visibleUtrvs: ObjectId[]; utrCodes: string[] }) {
    const aggregations = [
      {
        $match: {
          _id: { $in: visibleUtrvs },
          ...excludeSoftDeleted(),
        },
      },
      universalTrackerLookup,
      {
        $match: {
          'universalTracker.code': { $in: utrCodes },
        },
      },
      {
        $project: {
          _id: 1,
          universalTracker: {
            $arrayElemAt: ['$universalTracker', 0],
          },
        },
      },
    ];
    return this.universalTrackerValueModel.aggregate<Pick<UtrvData, '_id' | 'universalTracker'>>(aggregations).exec();
  }

  private async getUtrvsData({ utrvIds }: { utrvIds: ObjectId[] }): Promise<UtrvData[]> {
    const utrvsData = await this.universalTrackerValueModel
      .find({ _id: { $in: utrvIds }, ...excludeSoftDeleted() })
      .populate('universalTracker')
      .lean<UniversalTrackerValueExtended[]>()
      .exec();

    return utrvsData.map((d) => ({
      _id: d._id,
      value: d.value,
      valueData: d.valueData,
      status: d.status,
      effectiveDate: d.effectiveDate,
      universalTracker: d.universalTracker,
    }));
  }

  public async processSetupTask(job: AIReportDocumentJobModel, task: SetupIXBRLReportTask) {
    const { reportId, reportType } = task.data;
    // First get the report document to check if it has a specific surveyId
    const { surveyIds } = await ReportDocument.findById(task.data.reportId, { surveyIds: 1 }).orFail().lean().exec();

    this.logger.info('Retrieving survey data', {
      surveyIds,
      initiativeId: job.initiativeId,
    });

    const surveys = await this.surveyModel
      .find({
        ...(surveyIds ? { _id: { $in: surveyIds } } : {}),
        initiativeId: job.initiativeId,
        deletedDate: { $exists: false },
      })
      .sort({ effectiveDate: -1, created: -1 })
      .populate('initiative')
      .lean<SurveyModelPlainWithInitiative[]>()
      .exec();

    /**
     * Currently use information of 1 survey
     * @todo: Make the report aware of multiple surveys
     */
    if (surveys.length === 0) {
      this.logger.error(
        new ContextError('No survey found for the initiative associated with this report document.', {
          surveyIds,
          initiativeId: job.initiativeId,
        })
      );
      return;
    }
    const survey = surveys[0];

    // Figure out which data to use
    const externalMapping = await this.utrExternalMappingModel.find({ type: reportType }).lean();
    const externalXbrlMapping = externalMapping.reduce((acc, item) => {
      if (!acc[item.mappingCode]) {
        acc[item.mappingCode] = {
          factName: item.mappingCode,
          utrCode: item.utrs[0].utrCode,
          valueListCode: item.utrs[0].valueListCode,
        };
      }
      return acc;
    }, {} as XBRLMapping);

    // Merge default mapping with external mapping
    const mapping = getMappingsByType({ type: reportType, overrides: externalXbrlMapping });
    const utrCodes = Object.values(mapping).reduce((acc, item) => {
      if (!item) {
        return acc;
      }
      acc.push(item.utrCode);
      return acc;
    }, [] as string[]);

    const utrvsDataMin = await this.getUtrvsDataMin({ visibleUtrvs: survey.visibleUtrvs || [], utrCodes });
    const sections = getReportOutline(reportType);

    Object.entries(sections).forEach(([sectionKey, section]) => {
      if (!isDynamicSection(section)) {
        return;
      }
      const relatedTags = Array.from(
        new Set([...section.relatedTags, ...section.subsections.flatMap((s) => s.relatedTags)])
      );
      const relevantMapping = Object.values(mapping).reduce((acc, item) => {
        if (!item || !relatedTags.includes(item.factName)) {
          return acc;
        }
        acc[item.factName] = {
          ...item,
          utrvId: utrvsDataMin.find((d) => d.universalTracker.code === item.utrCode)?._id,
        };
        return acc;
      }, {} as XBRLMapping<ExtendedTagMappingItem>);

      const processSectionTask: ProcessIXBRLReportSectionTask = {
        id: generatedUUID(),
        name: `Process section ${section.heading} of ixbrl ${task.data.reportType} report`,
        type: TaskType.ProcessIXBRLReportSection,
        status: TaskStatus.Pending,
        data: {
          reportId,
          reportType,
          sectionKey,
          relevantMapping,
        },
      };
      job.tasks.push(processSectionTask);
    });

    const generateLexicalStateTask: GenerateReportLexicalStateTask = {
      id: generatedUUID(),
      name: `Generate lexical state of ixbrl ${task.data.reportType} report`,
      type: TaskType.GenerateReportLexicalState,
      status: TaskStatus.Pending,
      data: {
        reportId,
        reportType,
        reportContext: {
          initiativeName: survey.initiative.name,
          effectiveDate: customDateFormat(survey.effectiveDate, DateFormat.MonthDayYear, false),
        },
      },
    };
    job.tasks.push(generateLexicalStateTask);
  }

  public async generateReportSection({
    reportId,
    reportType,
    section,
    relevantMapping,
    modelName,
  }: {
    reportId: ObjectId;
    reportType: ReportDocumentType;
    section: DynamicSection;
    relevantMapping: XBRLMapping<ExtendedTagMappingItem>;
    modelName: UnifiedModelName;
  }): Promise<SectionBreakdown | undefined> {
    this.logger.info(`Generating report section using model ${modelName}`, {
      reportId,
      reportType,
      section: section.heading,
    });
    const aiModel = this.unifiedModelFactory.getFileSupportModel(modelName);
    const utrvIds = Object.values(relevantMapping)
      .map((item) => item?.utrvId)
      .filter((id) => id) as ObjectId[];
    const utrvsData = await this.getUtrvsData({ utrvIds });

    const response = await aiModel.executeWithFiles<typeof sectionBreakdownSchema>({
      systemPrompt: REPORT_OUTLINE_PROMPT[reportType].systemPrompt,
      prompt: REPORT_OUTLINE_PROMPT[reportType].userPrompt({
        reportOutline: section,
        sectionData: Object.values(relevantMapping).reduce((acc, item) => {
          const utrv = utrvsData.find((utrv) => utrv._id.equals(item?.utrvId));
          if (!item || !utrv) {
            return acc;
          }
          acc.push({
            relatedTag: item.factName,
            value: utrv.value,
            valueData: utrv.valueData,
            status: utrv.status,
            effectiveDate: utrv.effectiveDate,
          });
          return acc;
        }, [] as (Pick<UtrvData, 'value' | 'valueData' | 'status' | 'effectiveDate'> & { relatedTag: string })[]),
      }),
      files: [],
      jsonSchema: sectionBreakdownSchema,
      modelName,
    });

    this.logger.info('Report outline generated', {
      reportId,
      reportType,
      modelName,
      usage: response.tokenUsage,
    });

    return response.data;
  }

  public async processIXBRLReportSectionTask(_job: AIReportDocumentJobModel, task: ProcessIXBRLReportSectionTask) {
    const { reportId, reportType, sectionKey, relevantMapping } = task.data;

    const section = getReportOutline(reportType)[sectionKey];

    task.data.sectionBreakdown = isDynamicSection(section)
      ? await this.generateReportSection({
          reportId,
          reportType,
          section,
          relevantMapping,
          // @todo: make this configurable
          // "claude-sonnet-4-20250514" | "claude-opus-4-20250514" | "gemini-2.5-pro" | "gemini-2.5-flash"
          modelName: 'gemini-2.5-pro',
        })
      : undefined;
  }

  public async processReportLexicalStateTask(job: AIReportDocumentJobModel, task: GenerateReportLexicalStateTask) {
    const { reportId, reportType, reportContext } = task.data;
    const sectionsData: Record<string, SectionData> = {};

    for (const task of job.tasks) {
      if (task.type !== TaskType.ProcessIXBRLReportSection || !task.data.sectionBreakdown) {
        continue;
      }
      const utrvIds = Object.values(task.data.relevantMapping)
        .map((item) => item?.utrvId)
        .filter((id) => id) as ObjectId[];
      const utrvData = await this.getUtrvsData({ utrvIds });
      sectionsData[task.data.sectionKey] = {
        relevantMapping: task.data.relevantMapping,
        breakdown: task.data.sectionBreakdown,
        utrvData,
      };
    }

    this.logger.info('Generating report lexical state', {
      reportId,
      reportType,
    });
    task.data.lexicalState = await this.aiBreakdownBuilder.buildLexicalState({
      reportId,
      reportType,
      sectionsData,
      reportContext,
    });
  }

  public async processTask(
    job: AIReportDocumentJobModel,
    task: AIReportDocumentTask
  ): Promise<TaskResult<AIReportDocumentJobModel>> {
    await this.startTask(job, task);
    try {
      switch (task.type) {
        case TaskType.SetupIXBRLReport: {
          await this.processSetupTask(job, task);
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        case TaskType.ProcessIXBRLReportSection: {
          await this.processIXBRLReportSectionTask(job, task);
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        case TaskType.GenerateReportLexicalState: {
          await this.processReportLexicalStateTask(job, task);
          await this.reportDocumentManager.updateReportStatus({
            reportId: task.data.reportId,
            status: ReportDocumentStatus.Generated,
          });
          return { job: await this.completeTask(job, task), executeNextTask: true };
        }
        default:
          return {
            job: await this.failTask(
              job,
              task,
              new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
                jobId: job._id,
              })
            ),
            executeNextTask: true,
          };
      }
    } catch (error) {
      job.status = JobStatus.Error;
      /**
       * Background job fails, no data is generated, mark the report as "generated" (without data) to start syncing process
       * The end result should be the same as using blank template
       */
      await this.reportDocumentManager.updateReportStatus({
        reportId: task.data.reportId,
        status: ReportDocumentStatus.Generated,
      });
      return { job: await this.failTask(job, task, error as Error), executeNextTask: true };
    }
  }
}

let instance: AIReportDocumentWorkflow;
export const getAIReportDocumentWorkflow = () => {
  if (!instance) {
    instance = new AIReportDocumentWorkflow(
      wwgLogger,
      getReportDocumentManager(),
      Survey,
      UtrExternalMapping,
      UniversalTrackerValue,
      getAIBreakdownBuilder(),
      getUnifiedAIModelFactory()
    );
  }
  return instance;
};
