import type { ObjectId } from 'bson';
import type { BackgroundJobPlain, JobType, Task, TaskType } from '../../models/backgroundJob';
import type { HydratedDocument } from 'mongoose';
import type { ReportDocumentStatus, ReportDocumentType } from '../../models/reportDocument';
import type { SerializedEditorState } from 'lexical';
import type { ExtendedTagMappingItem, UtrvData, XBRLMapping } from '../reporting/types';

type ReportOutlineTaskData = {
  reportType: ReportDocumentType;
  reportId: ObjectId;
};

type XBRLTagSection = {
  title: string;
  description: string;
  keyHighlights: string[];
}

export type SectionBreakdown = {
  introduction: string;
  summary: string;
  xbrlTagSections: XBRLTagSection[];
  conclusion: string;
}

type ProcessSectionTaskData = ReportOutlineTaskData & {
  sectionKey: string;
  relevantMapping: XBRLMapping<ExtendedTagMappingItem>;
  sectionBreakdown?: SectionBreakdown;
};

export type ReportContext = {
  initiativeName: string;
  effectiveDate: string;
} 

type ReportLexicalStateTaskData = ReportOutlineTaskData & {
  lexicalState?: SerializedEditorState;
  reportContext: ReportContext;
};

export type SetupIXBRLReportTask = Task<ReportOutlineTaskData, TaskType.SetupIXBRLReport>;
export type ProcessIXBRLReportSectionTask = Task<ProcessSectionTaskData, TaskType.ProcessIXBRLReportSection>;
export type GenerateReportLexicalStateTask = Task<ReportLexicalStateTaskData, TaskType.GenerateReportLexicalState>;

export type AIReportDocumentTask =
  | SetupIXBRLReportTask
  | ProcessIXBRLReportSectionTask
  | GenerateReportLexicalStateTask;

export type AIReportDocumentJobPlain = Omit<BackgroundJobPlain<AIReportDocumentTask[]>, 'initiativeId'> & {
  type: JobType.AIReportDocument;
  initiativeId: ObjectId;
};

export type AIReportDocumentJobModel = HydratedDocument<AIReportDocumentJobPlain>;

export type InitializeLexicalState = {
  status: ReportDocumentStatus;
  lexicalState?: SerializedEditorState;
};

export type SectionData = {
  relevantMapping: XBRLMapping<ExtendedTagMappingItem>;
  breakdown: SectionBreakdown;
  utrvData: UtrvData[];
};
