/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import UniversalTracker, { type UniversalTrackerValueListPlainLean } from '../../models/universalTracker';
import { getBlueprintRepository, type BlueprintRepository } from '../../repository/BlueprintRepository';
import { activeBlueprints } from '../../survey/blueprints';
import { getHiddenStandardCodes } from '../utr/utrUtil';
import type { SearchUtrsRequest, SearchUtrsResponse, UTRPropertyFilter } from './ai-testing-types';
import type { FilterQuery, PipelineStage } from 'mongoose';
import BadRequestError from '../../error/BadRequestError';
import ContextError from '../../error/ContextError';
import { wwgLogger, type LoggerInterface } from '../wwgLogger';
import type { KeysEnum } from '../../models/public/projectionUtils';
import { buildUTRTypeConditions } from './utils/utr-query-builder';

/**
 * Interface for the selected UTR fields used in AI testing search
 * This ensures type safety between the projection and the interface
 */
type AITestingUTRProjection = Pick<UniversalTrackerValueListPlainLean,
  | '_id'
  | 'code'
  | 'name'
  | 'type'
  | 'description'
  | 'valueLabel'
  | 'valueType'
  | 'valueValidation'
  | 'tags'
  | 'blueprintCodes'
  | 'alternatives'
  | 'valueListOptions'
>

/**
 * Service for managing UTR search operations for AI testing
 */
export class AITestingUTRService {
  private logger: LoggerInterface;

  constructor(
    private blueprintRepository: BlueprintRepository,
    logger: LoggerInterface
  ) {
    this.logger = logger.child({ service: 'AITestingUTRService' });
  }

  /**
   * Search UTRs with filtering and pagination
   */
  async searchUTRs(request: SearchUtrsRequest): Promise<SearchUtrsResponse> {
    // Basic validation - allow empty query if types are provided
    if ((!request.query || typeof request.query !== 'string') && (!request.types || request.types.length === 0)) {
      throw new BadRequestError('Either query or types must be provided');
    }

    const limit = Math.min(request.limit || 100, 1000); // Default 100, cap at 1000
    const offset = request.offset || 0;

    // Build direct MongoDB query
    const query = request.query?.trim() || '';

    // Handle hidden standard codes (from existing UTR search pattern)
    const hiddenStandardCodes = getHiddenStandardCodes();

    // Get UTRs in scope (from blueprint system)
    const utrCodesInScope = await this.blueprintRepository.getSurveyUtrCodes(activeBlueprints);

    // Build MongoDB search query with explicit $and array
    const mongoQuery: FilterQuery<UniversalTrackerValueListPlainLean> & { $and: any[] } = {
      $and: [
        { code: { $in: utrCodesInScope } }
      ]
    };

    // Note: Text search is handled differently using aggregation pipeline
    // to properly search within alternatives object

    // Handle types filter - used by StandardsFrameworksSelector for metric pack filtering
    if (request.types && Array.isArray(request.types) && request.types.length > 0) {
      const typeConditions = buildUTRTypeConditions(request.types);
      if (typeConditions) {
        mongoQuery.$and.push(typeConditions);
      }
    }

    // Add property filters
    if (request.properties && Array.isArray(request.properties) && request.properties.length > 0) {
      this.addPropertyFilters(mongoQuery, request.properties);
    }

    // Exclude hidden standard codes
    if (hiddenStandardCodes.length > 0) {
      mongoQuery.$and.push({ type: { $nin: hiddenStandardCodes } });
    }

    // Execute search - use aggregation for text search, find() for filters only
    let serviceResponse;
    try {
      if (query) {
        // Use aggregation pipeline for text search to properly search alternatives
        serviceResponse = await this.searchWithAggregation(
          mongoQuery.$and,
          query,
          limit,
          offset
        );
      } else {
        // Use regular find() for better performance when no text search
        const directResults = await UniversalTracker.find(mongoQuery)
          .limit(limit)
          .skip(offset)
          .select({
              _id: 1,
              code: 1,
              name: 1,
              type: 1,
              description: 1,
              valueLabel: 1,
              valueType: 1,
              valueValidation: 1,
              tags: 1,
              blueprintCodes: 1,
              alternatives: 1,
              valueListOptions: 1,
            } satisfies KeysEnum<AITestingUTRProjection>)
          .lean();

        const totalCount = await UniversalTracker.countDocuments(mongoQuery);

        serviceResponse = {
          utrs: directResults,
          total: totalCount,
          cached: false
        };
      }

    } catch (searchError) {
      this.logger.error(new ContextError('MongoDB search failed', {
        cause: searchError
      }));
      // Return empty results on error
      serviceResponse = {
        utrs: [],
        total: 0,
        hasMore: false
      };
    }

    // Transform results to match expected format and determine match source
    const transformedUtrs = serviceResponse.utrs.map((utr) => {
      // Check if this UTR matched through alternatives
      let matchedVia: string | undefined;
      if (request.types && request.types.length > 0 && utr.alternatives) {
        // Check if the UTR's type directly matches any of the requested types
        const directMatch = request.types.some(tf => tf.type === utr.type);
        if (!directMatch) {
          // Check which alternative key matched
          for (const typeFilter of request.types) {
            if (utr.alternatives[typeFilter.type]) {
              matchedVia = typeFilter.type;
              break;
            }
          }
        }
      }
      return this.transformUTR(utr, matchedVia);
    });

    return {
      utrs: transformedUtrs,
      total: serviceResponse.total,
      cached: serviceResponse.cached || false
    };
  }

  /**
   * Build base aggregation pipeline stages for text search
   */
  private buildTextSearchPipeline(initialFilters: any[], query: string): PipelineStage[] {
    const searchConditions = {
      $or: [
        { type: { $regex: query, $options: 'i' } },
        { code: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
        { valueLabel: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        // Search in alternatives
        { "alternativesArray.v.name": { $regex: query, $options: 'i' } },
        { "alternativesArray.v.valueLabel": { $regex: query, $options: 'i' } }
      ]
    };

    return [
      // First apply all non-text filters to reduce dataset
      { $match: { $and: initialFilters } },

      // Add alternatives as array for searching
      { $addFields: {
        alternativesArray: { $objectToArray: "$alternatives" }
      }},

      // Apply text search on regular fields and alternatives
      { $match: searchConditions }
    ];
  }

  /**
   * Search using aggregation pipeline to support alternatives searching
   */
  private async searchWithAggregation(
    initialFilters: any[],
    query: string,
    limit: number,
    offset: number
  ): Promise<{ utrs: any[]; total: number; cached: boolean }> {
    // Build base query pipeline
    const baseQuery = this.buildTextSearchPipeline(initialFilters, query);

    // Build pipeline for fetching results
    const pipeline: PipelineStage[] = [
      ...baseQuery,
      // Remove temporary field
      { $project: {
        alternativesArray: 0
      }},
      // Apply pagination
      { $skip: offset },
      { $limit: limit }
    ];

    // Build pipeline for counting total results
    const countPipeline: PipelineStage[] = [
      ...baseQuery,
      { $count: 'total' }
    ];

    // Execute both queries in parallel for better performance
    const [results, countResult] = await Promise.all([
      UniversalTracker.aggregate<AITestingUTRProjection>(pipeline).exec(),
      UniversalTracker.aggregate<{ total: number }>(countPipeline).exec()
    ]);

    const total = countResult[0]?.total || 0;

    return {
      utrs: results,
      total,
      cached: false
    };
  }

  /**
   * Add property filters to MongoDB query
   */
  private addPropertyFilters(mongoQuery: { $and: unknown[] }, properties: UTRPropertyFilter[]): void {
    properties.forEach((prop) => {
      const propQuery: Record<string, unknown> = {};

      switch (prop.operator) {
        case 'equals':
          propQuery[prop.path] = prop.value;
          break;
        case 'contains':
          propQuery[prop.path] = { $regex: prop.value, $options: 'i' };
          break;
        case 'exists':
          propQuery[prop.path] = { $exists: true };
          break;
        case 'notExists':
          propQuery[prop.path] = { $exists: false };
          break;
        case 'in':
          propQuery[prop.path] = { $in: Array.isArray(prop.value) ? prop.value : [prop.value] };
          break;
        case 'notIn':
          propQuery[prop.path] = { $nin: Array.isArray(prop.value) ? prop.value : [prop.value] };
          break;
        case 'gt':
          propQuery[prop.path] = { $gt: prop.value };
          break;
        case 'lt':
          propQuery[prop.path] = { $lt: prop.value };
          break;
        default:
          propQuery[prop.path] = { $regex: prop.value, $options: 'i' };
      }

      mongoQuery.$and.push(propQuery);
    });
  }

  /**
   * Transform raw UTR data to response format
   */
  private transformUTR(utr: AITestingUTRProjection, matchedVia?: string): SearchUtrsResponse['utrs'][0] {
    return {
      id: utr._id.toString(),
      name: utr.name || '',
      code: utr.code || '',
      type: utr.type || '',
      typeCode: utr.blueprintCodes?.[0] || utr.type,
      description: utr.description || '',
      valueLabel: utr.valueLabel || '',
      valueType: utr.valueType || '',
      matchedVia: matchedVia,
      properties: {
        valueValidation: utr.valueValidation,
        tags: utr.tags ? Object.values(utr.tags).flat() : undefined,
        blueprintCodes: utr.blueprintCodes,
        valueListOptions: utr.valueListOptions ? [utr.valueListOptions] : undefined,
        alternatives: utr.alternatives,
      }
    };
  }
}

// Singleton instance
let instance: AITestingUTRService | undefined;

export const getAITestingUTRService = () => {
  if (!instance) {
    instance = new AITestingUTRService(
      getBlueprintRepository(),
      wwgLogger
    );
  }
  return instance;
};
