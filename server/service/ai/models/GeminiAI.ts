/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import config from '../../../config';
import { wwgLogger, type LoggerInterface } from '../../wwgLogger';
import UserError from '../../../error/UserError';
import ContextError from '../../../error/ContextError';
import BadRequestError from '../../../error/BadRequestError';
import type { UnifiedFileSupportAIModel, FileUploadResult, FileSupportAiModelWithCapabilities } from './FileSupportAiModel';
import type { AIPrompt, AIResponse, AIFileInfo } from './UnifiedAIModel';
import type { AIModel } from './AIModel';
import type { ResponseFormat } from '../../../routes/validation-schemas/ai-response-formats/ai-assistant';
import type { ZodType, z } from 'zod';
import { GoogleGenAI, Type, type Part, type Content, type FileData, type GenerateContentConfig } from '@google/genai';
import type { Uploadable } from 'openai/uploads';
import type { FilePurpose, FileObject } from 'openai/resources/files';
import type { MessageCreateParams } from 'openai/resources/beta/threads/messages';
import type { AssistantCreateParams } from 'openai/resources/beta/assistants';
import { AiProvider } from '../types';

// Gemini model configurations - only latest models
export const GEMINI_MODELS = {
  'gemini-2.5-pro': { tokenLimit: 1048576, outputLimit: 65536 },
  'gemini-2.5-flash': { tokenLimit: 1048576, outputLimit: 65536 },
} as const;

export type GeminiModelType = keyof typeof GEMINI_MODELS;

const DEFAULT_MODEL: GeminiModelType = 'gemini-2.5-flash';

/**
 * Gemini AI implementation using the unified interface pattern
 * Follows Google's SDK pattern where model is passed per request
 * Also implements legacy interfaces for backward compatibility
 */
export class GeminiAI implements UnifiedFileSupportAIModel, AIModel, FileSupportAiModelWithCapabilities {
  private client: GoogleGenAI;
  private uploadedFiles: Map<string, {
    uri: string;
    mimeType: string;
    name: string;
    state?: string;
    sizeBytes?: string;
    createTime?: string;
  }> = new Map();

  constructor(
    private logger: LoggerInterface,
    client?: GoogleGenAI
  ) {
    this.client = client || new GoogleGenAI({ apiKey: config.ai.gemini.apiKey });
  }

  public getProvider(): AiProvider {
    return AiProvider.Gemini;
  }

  public supportsModel(model: string): boolean {
    return !!GEMINI_MODELS[model as GeminiModelType];
  }

  public getSupportedModels(): string[] {
    return Object.keys(GEMINI_MODELS);
  }

  // Legacy FileSupportAiModel interface implementations
  public async createFile(params: { file: Uploadable; purpose?: FilePurpose }): Promise<FileObject> {
    const result = await this.uploadFile(params.file as any, {
      purpose: params.purpose
    });
    return {
      id: result.id,
      bytes: result.size || 0,
      created_at: Math.floor((result.createdAt?.getTime() || Date.now()) / 1000),
      filename: result.name || 'file',
      object: 'file',
      purpose: params.purpose || 'assistants' as any,
      status: 'processed',
      status_details: undefined
    };
  }

  public async deleteFile(fileId: string): Promise<any> {
    // Gemini doesn't support file deletion via API
    this.uploadedFiles.delete(fileId);
    this.logger.info('File reference removed from Gemini', { fileId });
    // Return in OpenAI FileDeleted format for legacy compatibility
    return {
      id: fileId,
      object: 'file' as const,
      deleted: true
    };
  }

  public async retrieveFile(fileId: string): Promise<FileObject | null> {
    const exists = await this.fileExists(fileId);
    if (!exists) return null;
    return {
      id: fileId,
      object: 'file',
      bytes: 0,
      created_at: Math.floor(Date.now() / 1000),
      filename: 'file',
      purpose: 'assistants',
      status: 'processed',
      status_details: undefined
    };
  }

  public async createAssistant(body: Partial<AssistantCreateParams>): Promise<any> {
    // Gemini doesn't need assistants, return mock
    return {
      id: 'gemini-no-assistant',
      object: 'assistant',
      created_at: Math.floor(Date.now() / 1000),
      name: body.name || 'Gemini Assistant',
      model: DEFAULT_MODEL,
      instructions: body.instructions || '',
      tools: body.tools || [],
      metadata: {}
    };
  }

  public async deleteAssistant(assistantId: string): Promise<any> {
    return { id: assistantId, object: 'assistant.deleted', deleted: true };
  }

  public async runThreadWithAssistant(params: {
    assistantId: string;
    message: MessageCreateParams;
    instructions?: string;
    jsonSchema?: ZodType;
  }): Promise<any> {
    const fileIds = params.message.attachments?.map((att: any) => att.file_id) || [];
    if (!params.jsonSchema) {
      throw new UserError('jsonSchema is required for runThreadWithAssistant');
    }
    return this.executeWithFiles({
      prompt: params.message.content as string,
      files: fileIds.map(id => ({ fileId: id })),
      jsonSchema: params.jsonSchema
    });
  }

  public supportsAssistants(): boolean {
    return false;
  }

  public getModelVersion(): string {
    return DEFAULT_MODEL;
  }

  // Unified methods with proper overloading
  public async parseCompletion<T = any>(messages: AIPrompt[], maxTokens?: number, responseFormat?: ResponseFormat): Promise<AIResponse<T>>;
  public async parseCompletion<T = any>(model: string, messages: AIPrompt[], options?: { maxTokens?: number; temperature?: number; responseFormat?: ResponseFormat }): Promise<AIResponse<T>>;
  public async parseCompletion<T = any>(
    modelOrMessages: string | AIPrompt[],
    messagesOrMaxTokens?: AIPrompt[] | number,
    optionsOrResponseFormat?: { maxTokens?: number; temperature?: number; responseFormat?: ResponseFormat } | ResponseFormat
  ): Promise<AIResponse<T>> {
    if (typeof modelOrMessages === 'string') {
      // Unified interface: parseCompletion(model, messages, options)
      const model = modelOrMessages;
      const messages = messagesOrMaxTokens as AIPrompt[];
      const options = optionsOrResponseFormat as { maxTokens?: number; temperature?: number; responseFormat?: ResponseFormat } | undefined;

      const response = await this.runCompletion(model, messages, options);
      try {
        const parsed = JSON.parse(response.content) as T;
        return {
          ...response,
          content: parsed
        };
      } catch (error) {
        throw new ContextError('Failed to parse Gemini response as JSON', {
          responseContent: response.content,
          cause: error
        });
      }
    } else {
      // Legacy interface: parseCompletion(messages, maxTokens, responseFormat)
      const messages = modelOrMessages;
      const maxTokens = messagesOrMaxTokens as number | undefined;
      const responseFormat = optionsOrResponseFormat as ResponseFormat | undefined;
      return this.parseCompletion(DEFAULT_MODEL, messages, { maxTokens, responseFormat });
    }
  }

  public async runCompletion(messages: AIPrompt[], maxTokens?: number): Promise<AIResponse>;
  public async runCompletion(model: string, messages: AIPrompt[], options?: { maxTokens?: number; temperature?: number; responseFormat?: ResponseFormat }): Promise<AIResponse>;
  public async runCompletion(
    modelOrMessages: string | AIPrompt[],
    messagesOrMaxTokens?: AIPrompt[] | number,
    options?: { maxTokens?: number; temperature?: number; responseFormat?: ResponseFormat }
  ): Promise<AIResponse> {
    if (typeof modelOrMessages === 'string') {
      // Unified interface: runCompletion(model, messages, options)
      const model = modelOrMessages;
      const messages = messagesOrMaxTokens as AIPrompt[];

      if (!this.supportsModel(model)) {
        throw new UserError(`Unsupported Gemini model: ${model}. Supported models: ${this.getSupportedModels().join(', ')}`);
      }

      try {
        const promptText = this.convertMessagesToGeminiFormat(messages);
        const modelConfig = GEMINI_MODELS[model as GeminiModelType];

        // Use consistent structure with executeWithFiles
        const contents: Content[] = [{
          role: 'user',
          parts: [{ text: promptText }]
        }];

        const response = await this.client.models.generateContent({
          model,
          contents,
          config: {
            maxOutputTokens: options?.maxTokens || modelConfig.outputLimit,
            temperature: options?.temperature || 1.0,
          }
        });

        const text = response.text || '';

        // Simple token estimation
        const promptTokens = Math.ceil(promptText.length / 4);
        const completionTokens = Math.ceil(text.length / 4);

        return {
          content: text,
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: promptTokens + completionTokens
          }
        };
      } catch (error) {
        throw new UserError('Unable to communicate with Gemini AI. Please try again later', { cause: error });
      }
    } else {
      // Legacy interface: runCompletion(messages, maxTokens)
      const messages = modelOrMessages;
      const maxTokens = messagesOrMaxTokens as number | undefined;
      return this.runCompletion(DEFAULT_MODEL, messages, { maxTokens });
    }
  }


  // File operations
  public async uploadFile(
    file: Buffer | Blob | string,
    metadata?: {
      filename?: string;
      mimeType?: string;
      purpose?: string;
    }
  ): Promise<FileUploadResult> {
    try {
      const fileData = await this.prepareFileData(file, metadata);

      // Convert Buffer to Blob for Gemini API
      let blob: Blob;
      if (fileData.buffer) {
        blob = new Blob([fileData.buffer], { type: fileData.mimeType });
      } else if (fileData.blob) {
        blob = fileData.blob;
      } else {
        throw new Error('No file data available - neither buffer nor blob was provided');
      }

      // Upload to Gemini
      const uploadResponse = await this.client.files.upload({
        file: blob,
        config: {
          mimeType: fileData.mimeType,
          displayName: fileData.filename
        }
      });

      // Validate response
      if (!uploadResponse || !uploadResponse.name) {
        throw new ContextError('Invalid upload response from Gemini', {
          response: uploadResponse,
          filename: fileData.filename
        });
      }

      // Store file info - use the actual Gemini file name/ID
      const fileId = uploadResponse.name; // Gemini returns IDs like "files/..."

      // Ensure we have the correct URI format
      let fileUri = uploadResponse.uri;
      if (!fileUri && uploadResponse.name) {
        // If no URI provided, construct it from the name
        fileUri = uploadResponse.name.startsWith('http')
          ? uploadResponse.name
          : `https://generativelanguage.googleapis.com/v1beta/${uploadResponse.name}`;
      }

      this.uploadedFiles.set(fileId, {
        uri: fileUri || '',
        mimeType: uploadResponse.mimeType || fileData.mimeType,
        name: fileData.filename,
        state: uploadResponse.state || 'ACTIVE',
        sizeBytes: uploadResponse.sizeBytes,
        createTime: uploadResponse.createTime
      });

      this.logger.info('File uploaded to Gemini', { fileId, name: fileData.filename });

      return {
        id: fileId, // Return the actual Gemini file ID without prefix
        name: fileData.filename,
        size: parseInt(uploadResponse.sizeBytes || '0'),
        createdAt: uploadResponse.createTime ? new Date(uploadResponse.createTime) : new Date()
      };
    } catch (error) {
      throw new ContextError('Gemini file upload failed', { cause: error });
    }
  }

  public async fileExists(fileId: string): Promise<boolean> {
    return this.uploadedFiles.has(fileId);
  }

  // Combined executeWithFiles implementation to satisfy both interfaces
  public async executeWithFiles<Output extends ZodType>(params: { prompt: string; files: AIFileInfo[]; jsonSchema: Output; maxTokens?: number; temperature?: number; systemPrompt?: string; vectorStoreId?: string; modelName?: string; }): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }>;
  public async executeWithFiles<Output extends ZodType>(model: string, params: { prompt: string; files: AIFileInfo[]; jsonSchema: Output; maxTokens?: number; temperature?: number; systemPrompt?: string; vectorStoreId?: string; }): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }>;
  public async executeWithFiles<Output extends ZodType>(
    modelOrParams: string | { prompt: string; files: AIFileInfo[]; jsonSchema: Output; maxTokens?: number; temperature?: number; systemPrompt?: string; vectorStoreId?: string; modelName?: string; },
    params?: { prompt: string; files: AIFileInfo[]; jsonSchema: Output; maxTokens?: number; temperature?: number; systemPrompt?: string; vectorStoreId?: string; }
  ): Promise<{
    data: z.infer<Output> | undefined;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    let model: string;
    let actualParams: { prompt: string; files: AIFileInfo[]; jsonSchema: Output; maxTokens?: number; temperature?: number; systemPrompt?: string; vectorStoreId?: string; modelName?: string; };
    // Note: vectorStoreId is ignored for Gemini as it doesn't use vector stores

    if (typeof modelOrParams === 'string') {
      // Unified interface: executeWithFiles(model, params)
      model = modelOrParams;
      if (!params) {
        throw new UserError('Parameters are required when model is specified');
      }
      actualParams = params;
    } else {
      // Legacy interface: executeWithFiles(params) with optional modelName
      model = modelOrParams.modelName || DEFAULT_MODEL;
      actualParams = modelOrParams;
    }

    if (!this.supportsModel(model)) {
      throw new UserError(`Unsupported Gemini model: ${model}`);
    }

    // Define parts outside try block so it's accessible in catch
    const parts: Part[] = [{ text: actualParams.prompt }];

    // Log what files we're trying to use
    this.logger.info('Building Gemini request with files:', {
      fileIds: actualParams.files.map(f => f.fileId),
      uploadedFilesKeys: Array.from(this.uploadedFiles.keys())
    });

    // Add file references
    for (const fileInfo of actualParams.files) {
      const fileData = this.uploadedFiles.get(fileInfo.fileId);
      if (fileData) {
        this.logger.info('Found file in cache:', { fileId: fileInfo.fileId, uri: fileData.uri, mimeType: fileData.mimeType });
        parts.push({
          fileData: {
            fileUri: fileData.uri,
            mimeType: fileData.mimeType
          } as FileData
        });
      } else {
        this.logger.warn('File not found in local cache', { fileId: fileInfo.fileId });
      }
    }

    try {
      // Convert Zod schema to Gemini response schema
      let responseSchema;
      try {
        responseSchema = this.zodToGeminiSchema(actualParams.jsonSchema);
      } catch (schemaError) {
        this.logger.warn('Failed to convert Zod schema to Gemini format, proceeding without schema:', {
          error: schemaError.message
        });
        // Continue without schema validation
      }

      const contents: Content[] = [{
        role: 'user',
        parts
      }];

      // Log the full request for debugging
      this.logger.info('Sending request to Gemini:', {
        model,
        contentsLength: contents.length,
        partsDetail: parts.map(p => {
          if ('text' in p && p.text) return { type: 'text', length: p.text.length };
          if ('fileData' in p && p.fileData) return { type: 'file', uri: p.fileData.fileUri, mimeType: p.fileData.mimeType };
          return { type: 'unknown' };
        }),
        maxOutputTokens: actualParams.maxTokens || GEMINI_MODELS[model as GeminiModelType].outputLimit,
        hasResponseSchema: !!responseSchema
      });

      // Build config object conditionally
      const config: GenerateContentConfig = {
        maxOutputTokens: actualParams.maxTokens || GEMINI_MODELS[model as GeminiModelType].outputLimit,
        temperature: actualParams.temperature || 0.7,
        responseMimeType: 'application/json'
      };

      // Add system instruction if provided
      const defaultSystemPrompt = 'You are a sustainability analysis expert specializing in ESG reporting and metrics extraction. Analyze documents carefully to identify relevant sustainability data and match it to the appropriate Universal Trackers (UTRs).';
      config.systemInstruction = actualParams.systemPrompt || defaultSystemPrompt;

      // Only add responseSchema if it was successfully generated
      if (responseSchema) {
        config.responseSchema = responseSchema;
      }

      // Retry logic for 500 errors
      let response;
      let lastError;
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          response = await this.client.models.generateContent({
            model,
            contents,
            config
          });
          break; // Success, exit retry loop
        } catch (error) {
          lastError = error;
          if (error.message && error.message.includes('"code":500') && attempt < maxRetries) {
            this.logger.warn(`Gemini API returned 500 error, retrying (attempt ${attempt}/${maxRetries})...`);
            // Wait a bit before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          } else {
            throw error; // Not a 500 error or max retries reached
          }
        }
      }

      if (!response && lastError) {
        throw lastError;
      }

      const text = response?.text || '{}';
      const result = JSON.parse(text);

      // Validate against schema
      const parsed = actualParams.jsonSchema.safeParse(result);
      if (!parsed.success) {
        // Log validation warning but don't throw - return the raw result
        this.logger.warn('Gemini response validation failed, returning raw response', {
          errors: parsed.error.errors,
          response: result,
          prompt: actualParams.prompt.substring(0, 200) + '...'
        });
        
        // Return the raw result so frontend can display it
        // The frontend has fallback handling for unexpected formats
        return result as z.infer<Output>;
      }

      const tokenUsage = response?.usageMetadata ? {
        inputTokens: response.usageMetadata.promptTokenCount ?? 0,
        outputTokens: response.usageMetadata.candidatesTokenCount ?? 0,
        totalTokens: response.usageMetadata.totalTokenCount ?? 0,
      } : undefined;

      return {
        data: parsed.data,
        tokenUsage,
      };
    } catch (error) {
      // Don't wrap errors that are already well-formed
      if (error instanceof ContextError || error instanceof UserError || error instanceof BadRequestError) {
        throw error;
      }

      // For Gemini API errors, extract the actual error message
      if (error.message && error.message.includes('[')) {
        // Gemini errors often have format: [400 Bad Request] {...}
        throw new UserError(`Gemini API error: ${error.message}`, { cause: error });
      }

      // Generic error with more context
      throw new ContextError(`Failed to execute Gemini request with files: ${error.message}`, {
        cause: error,
        fileIds: actualParams.files.map(f => f.fileId),
        model,
        partsLength: parts.length,
        response: error.response?.data,
        status: error.response?.status
      });
    }
  }

  // Helper methods
  private async prepareFileData(
    file: Buffer | Blob | string,
    metadata?: { filename?: string; mimeType?: string }
  ): Promise<{ buffer?: Buffer; blob?: Blob; filename: string; mimeType: string }> {
    let buffer: Buffer | undefined;
    let blob: Blob | undefined;
    let filename = metadata?.filename || 'file';
    let mimeType = metadata?.mimeType || '';

    if (Buffer.isBuffer(file)) {
      buffer = file;
    } else if (file instanceof Blob) {
      blob = file;
      mimeType = mimeType || file.type;
    } else if (typeof file === 'string') {
      // File path
      const fs = await import('fs/promises');
      const path = await import('path');
      buffer = await fs.readFile(file);
      filename = path.basename(file);
      mimeType = mimeType || this.getMimeTypeFromExtension(filename);
    } else {
      throw new UserError('Invalid file input type');
    }

    // Validate MIME type
    if (!mimeType || !this.isSupportedMimeType(mimeType)) {
      throw new UserError(`Unsupported file type: ${mimeType || 'unknown'}`);
    }

    return { buffer, blob, filename, mimeType };
  }

  private getMimeTypeFromExtension(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const mimeTypes: Record<string, string> = {
      'txt': 'text/plain',
      'pdf': 'application/pdf',
      'json': 'application/json',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'heic': 'image/heic',
      'heif': 'image/heif',
      'mp3': 'audio/mpeg',
      'mp4': 'video/mp4',
      'csv': 'text/csv',
    };
    return mimeTypes[ext || ''] || '';
  }

  private isSupportedMimeType(mimeType: string): boolean {
    const supportedTypes = new Set([
      'text/plain',
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/heic',
      'image/heif',
      'audio/mpeg',
      'video/mp4',
      'text/csv',
      'application/json'
    ]);
    return supportedTypes.has(mimeType);
  }

  private convertMessagesToGeminiFormat(messages: AIPrompt[]): string {
    return messages.map(msg => {
      if (msg.role === 'system') {
        return `System: ${msg.content}`;
      } else if (msg.role === 'user') {
        return `User: ${msg.content}`;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  private zodToGeminiSchema(zodSchema: ZodType): any {
    // Convert Zod schema to Gemini Type schema
    const def = (zodSchema as any)._def;
    const typeName = def?.typeName;

    if (typeName === 'ZodObject') {
      const properties: Record<string, any> = {};
      const required: string[] = [];

      const shape = def.shape();
      for (const [key, value] of Object.entries(shape)) {
        properties[key] = this.zodFieldToGeminiType(value as ZodType);
        const fieldDef = (value as any)._def;
        if (!fieldDef?.typeName?.includes('Optional')) {
          required.push(key);
        }
      }

      return {
        type: Type.OBJECT,
        properties,
        required: required.length > 0 ? required : undefined
      };
    } else if (typeName === 'ZodArray') {
      return {
        type: Type.ARRAY,
        items: this.zodFieldToGeminiType(def.type)
      };
    }

    return { type: Type.OBJECT };
  }

  private zodFieldToGeminiType(field: ZodType): any {
    const typeName = (field as any)._def.typeName;

    switch (typeName) {
      case 'ZodString':
        return { type: Type.STRING };
      case 'ZodNumber':
        return { type: Type.NUMBER };
      case 'ZodBoolean':
        return { type: Type.BOOLEAN };
      case 'ZodArray':
        return {
          type: Type.ARRAY,
          items: this.zodFieldToGeminiType((field as any)._def.type)
        };
      case 'ZodObject':
        return this.zodToGeminiSchema(field);
      case 'ZodOptional':
        return this.zodFieldToGeminiType((field as any)._def.innerType);
      default:
        return { type: Type.STRING };
    }
  }
}

// Singleton instance
let instance: GeminiAI | undefined;

export const getGeminiAI = () => {
  if (!instance) {
    instance = new GeminiAI(wwgLogger);
  }
  return instance;
};
