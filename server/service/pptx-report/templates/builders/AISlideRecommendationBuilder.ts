import { zodResponseFormat } from 'openai/helpers/zod';
import { type UniversalTrackerPlain } from '../../../../models/universalTracker';
import { z } from 'zod';
import { type AIModel, type AIPrompt } from '../../../ai/models/AIModel';
import { type HelperUtrv } from '../PPTXTemplateSurveyCache';
import { type PPTXTemplateSurveyCacheManager } from '../PPTXTemplateSurveyCacheManager';
import { type TableColumn, UtrValueType } from '../../../../models/public/universalTrackerType';
import { ActionList } from '../../../../types/constants';
import { SupportedMeasureUnits } from '../../../../types/units';

export enum SlideSection {
  Environmental = 'environmental',
  Social = 'social',
  Governance = 'governance',
  Economic = 'economic',
}

enum SlideChartType {
  Donut = 'donut',
  Bar = 'bar',
  Column = 'column',
}

const CHART_TYPE_TO_SLIDE_ID = {
  [SlideChartType.Donut]: 41,
  [SlideChartType.Bar]: 42,
  [SlideChartType.Column]: 43,
};

const slideRecommendationDto = z.object({
  result: z.array(
    z.object({
      category: z.string(),
      heading: z.string(),
      chartType: z.nativeEnum(SlideChartType),
      chartTitle: z.string(),
      utrs: z.array(
        z.object({
          utrCode: z.string(),
          valueType: z.nativeEnum(UtrValueType),
        })
      ),
    })
  ),
});

const DEFAULT_MAX_TOKEN = 10000;

type AnsweredUtr = Pick<
  UniversalTrackerPlain,
  'code' | 'valueLabel' | 'valueType' | 'instructions' | 'unitType' | 'unit' | 'numberScale'
> &
  Pick<HelperUtrv, 'status'> & { tableColumns?: TableColumn[] };

export interface PPTXSlideRecommendationUtr {
  category: string;
  chartTitle: string;
  heading: string;
  slideId: number;
  chartType: string;
  utrs: { utrCode: string; valueType: UtrValueType }[];
}

export class AISlideRecommendationBuilder {
  constructor(private aiModel: AIModel, private repositoryManager: PPTXTemplateSurveyCacheManager) {}

  public async getSlideRecommendations(): Promise<Map<SlideSection, PPTXSlideRecommendationUtr[]>> {
    const answeredUtrs = await this.getAnsweredUtrs();

    if (answeredUtrs.length === 0) {
      return new Map();
    }

    const prompt = this.generatePrompt(answeredUtrs);
    const responseFormat = zodResponseFormat(slideRecommendationDto, 'PPTXSlideRecommendation');
    const response = await this.aiModel.parseCompletion<{ result: PPTXSlideRecommendationUtr[] }>(
      [prompt],
      DEFAULT_MAX_TOKEN,
      responseFormat
    );
    return new Map([
      [
        SlideSection.Environmental,
        response.content?.result.map((slide) => ({
          ...slide,
          slideId: CHART_TYPE_TO_SLIDE_ID[slide.chartType as SlideChartType],
        })),
      ],
    ]);
  }

  private async getAnsweredUtrs() {
    const utrvsMap = await this.repositoryManager.getCachedSurvey()?.getUtrvsMap();

    if (!utrvsMap) {
      return [];
    }

    return Array.from(utrvsMap.values()).reduce((acc, utrv) => {
      // Skip private metrics from AI processing
      if (utrv.status === ActionList.Created || !utrv.universalTracker || utrv.isPrivate) {
        return acc;
      }

      acc.push({
        code: utrv.universalTracker.code,
        valueLabel: utrv.universalTracker.valueLabel,
        status: utrv.status,
        valueType: utrv.universalTracker.valueType,
        instructions: utrv.universalTracker.instructions,
        unitType: utrv.universalTracker.unitType,
        unit: utrv.universalTracker.unit,
        numberScale: utrv.universalTracker.numberScale,
        tableColumns: utrv.universalTracker.valueValidation?.table?.columns,
      });

      return acc;
    }, [] as AnsweredUtr[]);
  }

  private generatePrompt(answeredUtrs: AnsweredUtr[]): AIPrompt {
    const content = `You are an AI assistant that recommends the most impactful environmental metrics for a company's sustainability report.

      You will receive a JSON array of metric objects. Each object will have the following structure:
      {
        "code": "The unique code of the metric",
        "valueType": "The type of value the metric represents, such as ${Object.values(UtrValueType).join(', ')}",
        "valueLabel": "The title of the metric",
        "instructions": "Any specific instructions or context provided for the metric",
        "unitType": "The type of unit used for the metric, such as ${Object.values(SupportedMeasureUnits).join(
          ', '
        )}, etc.",
        "unit": "The specific unit of measurement",
        "numberScale": "The scale of the number, such as thousands, millions, etc."
        "status": "The status of the metric, such as ${Object.values(ActionList).join(', ')}"
        "tableColumns": "If the metric is a table, this is an array of the columns, each having a 'code' and a 'name'"
      }

      Here is the list of metrics: ${JSON.stringify(answeredUtrs)}

      Your primary task is to analyze a given list of company metrics and perform the following actions:

      Step 1: Classification
      - Iterate through the full list of input metrics. For each metric, determine if it is an environmental metric. Use the valueLabel, instructions, code, tableColumns and unitType fields to make this determination.
      - Keywords to look for: "Emissions", "GHG", "Scope 1", "Scope 2", "Scope 3", "Carbon", "CO2", "Energy", "Water", "Waste", "Effluents", "Recycling", "Consumption", "Spills", "Environmental", "Biodiversity", "Land use", "Air quality", "NOx", "SOx".
      - Explicitly discard any metric that is non-environmental, where the unitType is 'currency', or 'time' or other information indicates a social or economic topic (e.g., "Employee Turnover", "Revenue")
      - If the filtered list of environmental metrics is empty, the process stops here. Return a final JSON output of {"result": []} and do not proceed to the subsequent steps.

      Step 2: Scoring & Ranking
      - For each environmental metric identified in Step 1, evaluate its relevance from 0 to 100 and assign it to **score**. If the metric is not impactful enough for a high-level sustainability report, omit it from further processing.

      Step 3: Categorize
      - Group each scored environmental metric into a logical environmental category and assign it to **category** (e.g., 'GHG Emissions', 'Energy Management', 'Waste', 'Significant Air Emissions', 'Biodiversity').
      - If a metric does not fit into an existing category, create a new category

      Step 4: Output Generation and Filtering
      - From each category, you must select a strict maximum of the top three metrics based on the **score**
      - Determine the best chart type to visualize the metrics in each category, assign it to **chartType**, choose from: 'donut', 'bar', or 'column'.
      - Construct: Create the final output as a single JSON object with a key named "result". The value of "result" must be an array, where each element represents one of the final categories.
      Each object in the array must strictly adhere to the following structure:
        {
          "category": "The name of the category",
          "heading": Generate a concise, descriptive heading with a maximum of 5 words that summarizes the category's metrics.
          "chartType": "The chart type you suggested",
          "chartTitle": Generate a concise, descriptive chart title with a maximum of 5 words that summarizes the category's metrics.
          "utrs": [{
            "utrCode": The 'code' from the original input metric,
            "valueType": "The 'valueType' from the original input metric"
          }]
        }
      As a final step, arrange the categories array of "result" in order of their environmental significance.  
      Exclude any commentary or explanation — return only the structured JSON result as specified
    `;

    return {
      role: 'system',
      content,
    };
  }
}
