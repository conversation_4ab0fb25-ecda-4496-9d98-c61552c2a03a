/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { SurveyModelMinData, SurveyType } from '../../models/survey';
import { UserModel, UserPlain } from '../../models/user';
import Initiative, {
  CreateInitiativeData,
  DEFAULT_USAGE,
  InitiativePlain,
  InitiativeTags,
  MonthDay,
} from '../../models/initiative';
import { DefaultBlueprintCode } from '../../survey/blueprints';
import { createReportingCompanyCode, createSurveyCode } from '../../util/string';
import { InitiativeManager } from '../initiative/InitiativeManager';
import { UserRoles } from '../user/userPermissions';
import { SurveyImporter } from '../survey/SurveyImporter';
import { createStakeholderGroup } from '../stakeholder/StakeholderGroupManager';
import { SurveyScope } from '../survey/SurveyScope';
import { DataPeriods, UtrvType } from '../utr/constants';
import { getUserPermissionService } from "../user/UserPermissionService";
import { AddressPlain } from '../../models/organization';
import { processScopeChange } from '../survey/surveyDelegationProcess';
import { RequestScope } from '../survey/model/DelegationScope';
import { Actions } from '../action/Actions';
import { getRootInitiativeService, RootConfig, RootInitiativeService } from '../organization/RootInitiativeService';
import { InitiativePermissions } from '../initiative/InitiativePermissions';
import { AppConfig } from '../app/AppConfig';
import { ListcoService } from '../sgx/ListcoService.ts';
import { SurveyConfigService } from '../initiative/SurveyConfigService';
import InitiativeSettings from '../../models/initiativeSettings';
import { getSurveyDeadlineService, SurveyDeadlineService } from '../survey/SurveyDeadline';
import { FeatureCode } from '@g17eco/core';
import { toDate } from '../../util/date';
import { IndustryLevels } from '../../types/initiative';
import { getGroupCodesFromScopeGroups } from '../../util/survey';
import { InitiativeRepository } from '../../repository/InitiativeRepository';

export type SurveyCreateProps = Partial<Pick<SurveyModelMinData,
  | 'noteInstructionsEditorState'
  | 'noteInstructions'
  | 'isPrivate'
  | 'unitConfig'
  | 'scope'
  | 'evidenceRequired'
  | 'noteRequired'
  | 'verificationRequired'
  | 'sourceName'
  | 'name'
>> & {
  effectiveDate: string;
  deadlineDate?: string;
  scheduledDates?: {
    idempotencyKey?: string;
    date: string;
  }[];
  period: DataPeriods;
  domain?: string;
}

export interface ConfigFormData extends SurveyCreateProps {
  initiativeId?: string;
  initiativeName?: string;
  countryCode?: string; // country code
  countryInitiativeId?: string; // country code
  initiativeCountryCode: string,
  referenceCompany?: string,
  referralCode?: string,
  industry?: { icb2019?: IndustryLevels },
  permissionGroup: string,
  address: AddressPlain;
  registrationNumber: string,
  financialEndDate?: MonthDay,
  metadata?: {
    sgx_issuer_name?: string;
  }
  appConfig?: AppConfig;
}

export class SelfOnboardingManager {

  constructor(
    private rootService: RootInitiativeService,
    private surveyDeadlineService: SurveyDeadlineService,
    private userPermissionService: ReturnType<typeof getUserPermissionService>,
  ) {
  }

  public async createNewSurvey(initiative: InitiativePlain, user: UserPlain, data: SurveyCreateProps) {
    await this.processCompanySettings(initiative, data);
    return this.createSurvey(user, initiative, data);
  }

  private async createSurvey(
    user: UserPlain,
    initiative: InitiativePlain,
    data: SurveyCreateProps,
  ) {
    const rootConfig = await this.rootService.getConfig(initiative, { domain: data.domain });

    const effectiveDate = toDate(data.effectiveDate);
    const deadlineDate = data.deadlineDate ? toDate(data.deadlineDate) : undefined;

    const hasVerificationFeature = this.rootService.hasFeature(rootConfig, FeatureCode.Verification);

    const scheduledDates = this.surveyDeadlineService.processScheduledDates(data);

    const createData: SurveyModelMinData = {
      type: SurveyType.Default,
      scope: await this.updateSurveyScope(data, rootConfig),
      visibleUtrvs: [],
      code: createSurveyCode(initiative.code),
      name: data.name,
      sourceName: data.sourceName ?? DefaultBlueprintCode,
      period: data.period,
      effectiveDate,
      utrvType: UtrvType.Actual,
      visibleStakeholders: [user._id],
      unitConfig: SurveyImporter.mergeUnitConfig(initiative, data.unitConfig),
      initiativeId: initiative._id,
      stakeholders: createStakeholderGroup(),
      roles: { admin: [user._id], viewer: [] },
      evidenceRequired: Boolean(data.evidenceRequired),
      noteRequired: Boolean(data.noteRequired),
      verificationRequired: hasVerificationFeature && Boolean(data.verificationRequired),
      isPrivate: Boolean(data.isPrivate),
      noteInstructions: data.noteInstructions,
      noteInstructionsEditorState: data.noteInstructionsEditorState,
      deadlineDate,
      scheduledDates,
      creatorId: user._id,
    };

    const survey = await SurveyImporter.create(createData, user);

    // generate new scheduled notifications from survey template
    if (this.surveyDeadlineService.validate(survey)) {
      const createdScheduledDates = await this.surveyDeadlineService.bulkCreate({ survey, scheduledDates: survey.scheduledDates ?? [] });
      survey.scheduledDates = createdScheduledDates;
      await survey.save();
    }

    return survey;
  }

  public async updateSurveyScope(
    data: Pick<ConfigFormData, 'scope' | 'initiativeId'>,
    rootConfig: RootConfig,
  ) {
    const surveyScope = data.scope ?? SurveyScope.createEmpty();
    const scopeGroupsToAdd: RequestScope[] = [];

    rootConfig.survey.scope.filter(scope => scope.required).forEach(scope => {
      scopeGroupsToAdd.push(scope);
    });

    const metricGroupIds = getGroupCodesFromScopeGroups(scopeGroupsToAdd);
    const metricGroups =
      metricGroupIds.length > 0 && data.initiativeId
        ? await InitiativeRepository.getInitiativeMetricGroups(data.initiativeId, metricGroupIds)
        : [];

    return processScopeChange({ data: { action: Actions.Add, scopeGroups: scopeGroupsToAdd }, surveyScope, metricGroups });
  }

  public async processCompany(user: UserModel, data: ConfigFormData, options?: { extraTags?: string[] }) {

    if (data.initiativeId) {
      const initiative = await Initiative.findById(data.initiativeId).orFail().exec();

      const hasPermissions = await InitiativePermissions.canAccess(user, initiative._id);
      if (!hasPermissions) {
        throw new Error(`User does not have access to reporting company`);
      }

      return initiative;
    }

    if (!data.initiativeName) {
      throw new Error(`initiativeName name is required field`)
    }
    const tags = [InitiativeTags.Organization, ...(options?.extraTags ?? [])];
    const isEmptyFinancialEndDate =
      !data.financialEndDate || Object.values(data.financialEndDate).some((v) => !v || v === '-');

    const createData: CreateInitiativeData = {
      code: createReportingCompanyCode(data.initiativeName),
      name: data.initiativeName,
      tags: tags,
      usage: DEFAULT_USAGE,
      appConfigCode: data.appConfig?.code,
      industry: {
        gics: { level1: '', level2: '', level3: '', level4: '' },
        icb: { level1: '', level2: '', level3: '', level4: '' },
        icb2019: { level1: '', level2: '', level3: '', level4: '' },
        ...data.industry,
      },
      country: data.initiativeCountryCode,
      permissionGroup: data.permissionGroup,
      address: data.address,
      registrationNumber: data.registrationNumber,
      financialEndDate: isEmptyFinancialEndDate
        ? ListcoService.getFinancialEndDateByIssuerName(data.metadata?.sgx_issuer_name)
        : data.financialEndDate,
      metadata: data.metadata,
    };

    const createdInitiative = await InitiativeManager.create(createData);
    await this.userPermissionService.addAndUpdate({
      user,
      initiativeId: createdInitiative._id,
      permissions: [
        UserRoles.Owner,
        UserRoles.Manager,
        UserRoles.Verifier,
        UserRoles.Contributor
      ],
      context: { action: 'onboarding' }
    });

    return createdInitiative;
  }

  /**
   * Why do we need to do this on every request?
   */
  public async processCompanySettings(initiative: InitiativePlain, data: Pick<ConfigFormData, 'unitConfig'>) {
    const mergedUnitConfig = SurveyImporter.mergeUnitConfig(initiative, data.unitConfig);

    const existSettings = await InitiativeSettings.findOne({ initiativeId: initiative._id }).exec();

    if (!existSettings || !existSettings.surveyConfig) {
      return SurveyConfigService.update(String(initiative._id), {
        subsidiariesEnforced: false,
        evidenceRequired: true,
        verificationRequired: true,
        isPrivate: false,
        unitConfig: mergedUnitConfig,
      })
    }

    if (existSettings.surveyConfig.unitConfig) {
      return;
    }

    existSettings.surveyConfig.unitConfig = mergedUnitConfig;
    await existSettings.save();
    return existSettings;
  }
}

let instance: SelfOnboardingManager;
export const getSelfOnboardingManager = () => {
  if (!instance) {
    instance = new SelfOnboardingManager(
      getRootInitiativeService(),
      getSurveyDeadlineService(),
      getUserPermissionService(),
    );
  }

  return instance;
}

