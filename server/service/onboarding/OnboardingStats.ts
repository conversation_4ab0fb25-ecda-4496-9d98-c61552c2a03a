/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Onboarding from '../../models/onboarding';
import moment from 'moment';

export class StatsFilter {
  groupBy = 'status';
  timeUnit = 'day';

  constructor(
    public from: Date = moment().subtract(1, 'year').toDate(),
    public to: Date = moment().endOf('day').toDate()
  ) {
  }

}

export class OnboardingStats {

  public static async stats(filters: StatsFilter): Promise<any> {
    return Onboarding.aggregate([
      { $match: { created: { $gt: filters.from, $lte: filters.to } } },
      {
        $project: {
          day: { $substr: ['$created', 0, 10] },
          status: 1,
        }
      },
      {
        $group: {
          _id: { date: '$day', status: '$status' },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
  }
}
