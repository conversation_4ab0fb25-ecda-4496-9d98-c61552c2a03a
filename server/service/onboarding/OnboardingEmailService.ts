/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { createMailService, type MailerInterface, type MessageInterface } from '../email/EmailService';
import Onboarding, {
  isInitiativeOnboardingModel,
  isOrganizationOnboardingModel,
  type NotificationItem,
  ObNotificationCode,
  ObNotificationService,
  type OnboardingModel,
  type OnboardingModelPlain,
  type OnboardingWithNotifications,
  type OrganizationOnboardingModel,
  type OrganizationOnboardingPlain,
} from '../../models/onboarding';
import { getFullName, type UserPlain } from '../../models/user';
import baseTemplate from '../email/templates/core/baseTemplate';
import { type ObjectId } from 'bson';
import EmailTransaction, { EmailDeliverySubType, EmailDeliveryType } from '../email/model/EmailTransaction';
import config from '../../config';
import initial, { type EmailTemplate } from '../email/templates/onboarding/initial';
import organizationInitial, { type OrganizationEmailTemplate } from '../email/templates/onboarding/organizationInitial';
import { followUpEmail } from '../email/templates/onboarding/followUpEmail';
import { type CreateCrmOnboardingData, type CreateCrmOrganizationOnboardingData } from '../crm/contact';
import userCreated from '../email/templates/onboarding/admin/userCreated';
import { UrlMapper } from '../url/UrlMapper';
import { generateUnsubscribeToken } from '../subscription/token';
import { type DomainConfigRepository, getDomainConfigRepository } from '../organization/DomainConfigRepository';
import { type AppConfigService, getAppConfigService } from '../app/AppConfigService';
import { organizationFollowUpEmail } from '../email/templates/onboarding/organizationFollowUpEmail';
import Organization from '../../models/organization';
import ContextError from '../../error/ContextError';
import { type EmailTransactionService, getEmailTransactionService } from '../email/EmailTransactionService';
import BadRequestError from '../../error/BadRequestError';
import { OnboardingEmailMessage } from './constants';
import { subtractDate } from '../../util/date';
import { getRootInitiativeService, type RootInitiativeService } from '../organization/RootInitiativeService';
import { WWG } from '../app/company-tracker/WWG';

const RESEND_INTERVALS = 60 * 60 * 1000; // 1 hour between resend requests

export class OnboardingEmailService {

  constructor(
    private emailService: MailerInterface,
    private domainRepository: DomainConfigRepository,
    private appConfigService: AppConfigService,
    private emailTransactionService: EmailTransactionService,
    private rootInitiativeService: RootInitiativeService,
  ) {
  }

  public async sendOrganizationOnboardingInitial(
    onboarding: OrganizationOnboardingPlain,
    crmData: CreateCrmOrganizationOnboardingData
  ) {
    const domain = onboarding.metadata?.domain;
    const domainConfig = await this.domainRepository.getByDomain(domain);

    const user = onboarding.user;
    const data = organizationInitial({
      domain,
      domainConfig,
      firstName: user.firstName,
      emailTemplate: user.emailTemplate as OrganizationEmailTemplate,
      ...crmData,
    });

    const message = this.emailService.getNewMessageInstance();
    message.addTo(user.email, getFullName(user)).setHtml(data.body).setSubject(data.subject);

    return this.send(message, 'initial', user.userId, { onboardingId: onboarding._id });
  }

  public async sendOnboardingInitial(onboarding: OnboardingModelPlain, crmData: CreateCrmOnboardingData) {

    const domain = onboarding.metadata?.domain;
    const domainConfig = await this.domainRepository.getByDomain(domain)
    const appConfig = crmData.appConfigCode ? await this.appConfigService.getByCode(crmData.appConfigCode): undefined

    const user = onboarding.user;
    const data = initial({
      domain,
      domainConfig,
      firstName: user.firstName,
      emailTemplate: user.emailTemplate as EmailTemplate,
      appConfig,
      ...crmData
    });

    const message = this.emailService.getNewMessageInstance();
    message.addTo(user.email, getFullName(user))
      .setHtml(data.body)
      .setSubject(data.subject);

    return this.send(
      message,
      'initial',
      user.userId,
      { onboardingId: onboarding._id }
    );
  }

  private async appendNotificationAndSave(
    ob: OnboardingWithNotifications,
    code: ObNotificationCode,
    reference: { getId(): string }
  ) {
    const newItem: NotificationItem = {
      type: 'email',
      code,
      completedDate: new Date(),
      referenceId: reference.getId(),
    }
    ob.notifications.items.push(newItem);
    await ob.save();
    return newItem;
  }

  public async handleFollowUpEmail(ob: OnboardingWithNotifications, notificationCode: ObNotificationCode) {
    let reference;

    if (isInitiativeOnboardingModel(ob)) {
      if (!ob.initiativeId) {
        throw new ContextError('Invalid initiative onboarding', { onboardingId: ob._id });
      }
      const withInitiative = await ob.populate('initiative');
      reference = await this.sendFollowup(withInitiative, notificationCode);
      return this.appendNotificationAndSave(ob, notificationCode, reference);
    }

    if (isOrganizationOnboardingModel(ob)) {
      if (!ob.organizationId) {
        throw new ContextError('Invalid organization onboarding', { onboardingId: ob._id });
      }
      reference = await this.sendOrganizationFollowup(ob, notificationCode);
      return this.appendNotificationAndSave(ob, notificationCode, reference);
    }

    throw new ContextError('Unsupported onboarding type', { onboardingId: ob._id, type: ob.type });
  }

  private async sendFollowup(onboarding: OnboardingModel, action: ObNotificationCode) {

    const initiative = onboarding.initiative;
    if (!initiative) {
      throw new ContextError('Onboarding require initiative to be populated', { onboardingId: onboarding._id });
    }
    const domain = onboarding.metadata?.domain;
    const domainConfig = await this.domainRepository.getByDomain(domain);
    const org = await this.rootInitiativeService.getOrganization(initiative);
    const appConfig = org.appConfigCode ? await this.appConfigService.getByCode(org.appConfigCode) : undefined;

    const user = onboarding.user;
    const data = followUpEmail({
      domain,
      domainConfig,
      appConfig,
      action: action,
      firstName: user.firstName,
      emailTemplate: user.emailTemplate as EmailTemplate,
      initiativeName: initiative.name,
      onboardingUrl: UrlMapper.onboardingUrl({ token: onboarding.token, domain }),
      unsubscribeUrl: UrlMapper.onboardingUnsubscribeUrl({
        token: generateUnsubscribeToken(onboarding.user.email, onboarding.user.userId),
        domain: domain
      }),
    });

    const message = this.emailService.getNewMessageInstance();
    message.addTo(user.email, getFullName(user))
      .setHtml(data.body)
      .setSubject(data.subject);

    return this.send(
      message,
      action,
      user.userId,
      { onboardingId: onboarding._id }
    );
  }

  private async sendOrganizationFollowup(onboarding: OrganizationOnboardingModel, action: ObNotificationCode) {
    if (!onboarding.organizationId) {
      throw new ContextError('Organization Onboarding require organizationId', { onboardingId: onboarding._id });
    }

    const organization = await Organization.findById(onboarding.organizationId).orFail().exec();

    const domain = onboarding.metadata?.domain;
    const domainConfig = await this.domainRepository.getByDomain(domain);

    const user = onboarding.user;
    const data = organizationFollowUpEmail({
      domain,
      domainConfig,
      action: action,
      firstName: user.firstName,
      organizationName: organization.name,
      onboardingUrl: UrlMapper.onboardingUrl({ token: onboarding.token, domain }),
      unsubscribeUrl: UrlMapper.onboardingUnsubscribeUrl({
        token: generateUnsubscribeToken(onboarding.user.email, onboarding.user.userId),
        domain: domain
      }),
    });

    const message = this.emailService.getNewMessageInstance();
    message.addTo(user.email, getFullName(user))
      .setHtml(data.body)
      .setSubject(data.subject);

    return this.send(
      message,
      action,
      user.userId,
      { onboardingId: onboarding._id }
    );
  }

  private async send(message: MessageInterface, action: string, userId?: string | ObjectId, data?: any) {

    const emailResult = await this.emailService.send(message);

    EmailTransaction.create({
      externalId: emailResult.getId(),
      userId: userId ? userId.toString() : undefined,
      service: `onboarding_email_${action}`,
      data,
    }).catch(console.log);

    return emailResult;
  }

  async sendUserCreatedToSupport({ _id }: Pick<OnboardingModelPlain, '_id'>, user: UserPlain) {
    const data = {
      domain: undefined,
      domainConfig: undefined, // Do not care for internal staff
      appConfig: WWG, // use WWG appConfig for internal email
      article: { enabled: false },
      topContent: userCreated({ user }),
      link: {
        text: 'View user details',
        type: 'button',
        url: `${config.email.adminHostname}/dashboard/users/view/${user._id}`,
      },
      topContentHeader: '<br/>',
      user,
    };

    const html = baseTemplate(data);

    const message = this.emailService
      .getNewMessageInstance()
      .addTo(config.email.platformEmail, 'Platform Email')
      .setHtml(html)
      .setSubject(`G17Eco - New user created ${user.email}`);

    return this.send(message, 'user_created', user._id, { onboardingId: _id });
  }

  async sendJoinRequestRemoval(onboarding: OnboardingModel) {
    const user = onboarding.user;

    if (!onboarding.populated('initiative')) {
      await onboarding.populate('initiative');
    }
    const initiative = onboarding.initiative
    if (!initiative) {
      throw new Error(`Failed to populate initiative for onboarding ${onboarding._id}`)
    }
    const domain = onboarding.metadata?.domain;
    const domainConfig = await this.domainRepository.getByDomain(domain)
    const appConfig = initiative.appConfigCode ? await this.appConfigService.getByCode(initiative.appConfigCode): undefined


    const data = {
      domain,
      domainConfig,
      appConfig,
      topContent: `<div> Your request to join ${initiative.name} was rejected</div> `,
      topContentHeader: '<br/>',
      user,
      link: {
        text: 'GO TO PLATFORM',
        type: 'button',
        url: UrlMapper.relativeUrl('', domain)
      },
    };

    const html = baseTemplate(data);

    const message = this.emailService.getNewMessageInstance()
      .addTo(user.email, getFullName(user))
      .setHtml(html)
      .setSubject(`G17Eco - New user created ${user.email}`);

    return this.send(
      message,
      'request_join_reject',
      user.userId,
      { onboardingId: onboarding._id }
    );
  }

  private async getEmailTransactions(notificationItems: NotificationItem[]) {
    const transactionIds = notificationItems.reduce(
      (acc, notification) => (notification.referenceId ? [...acc, notification.referenceId] : acc),
      [] as string[]
    );
    return this.emailTransactionService.findAllByIds({
      transactionIds,
    });
  }

  public async getSentEmails({ onboardingId, initiativeId }: { onboardingId: ObjectId; initiativeId: ObjectId }) {
    const onboarding = await Onboarding.findOne({
      _id: onboardingId,
      $or: [
        { initiativeId },
        { 'user.permissions.initiativeId': initiativeId },
      ],
    })
      .orFail()
      .exec();
    const notificationItems = onboarding.notifications?.items;

    if (!notificationItems || notificationItems.length === 0) {
      return [];
    }

    const transactions = await this.getEmailTransactions(notificationItems);
    const transactionMap = new Map(transactions.map((t) => [t.externalId, t]));
    return notificationItems.map((item) => {
      const transaction = transactionMap.get(item.referenceId);
      if (transaction) {
        return {
          onboardingId: onboarding._id,
          externalId: item.referenceId,
          event: item.code,
          id: transaction.id,
          type: transaction.type,
          subType: transaction.subType,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
        };
      }
      return {
        onboardingId: onboarding._id,
        externalId: item.referenceId,
        event: item.code,
      };
    });
  }

  public async ensureAllowedToResend(onboarding: OnboardingModel) {
    const notificationItems = onboarding.notifications?.items ?? [];

    const hasSentAllReminders = [ObNotificationCode.FirstReminder, ObNotificationCode.FinalReminder].every((code) =>
      notificationItems.some((item) => item.code === code)
    );

    if (!hasSentAllReminders) {
      throw new BadRequestError(OnboardingEmailMessage.AutoReminderNotElapsed);
    }

    const transactions = await this.getEmailTransactions(notificationItems);
    if (transactions.length === 0) {
      throw new BadRequestError(OnboardingEmailMessage.AutoReminderNotElapsed);
    }

    const containHardBounce = transactions.some(
      (item) => item.type === EmailDeliveryType.Bounce && item.subType === EmailDeliverySubType.Permanent
    );

    if (containHardBounce) {
      throw new BadRequestError(OnboardingEmailMessage.EmailDeliveryFailure);
    }

    const latestManualReminder =
      notificationItems.length > 0
        ? notificationItems.reduce<NotificationItem | undefined>((latest, current) => {
            if (current.code !== ObNotificationCode.ManualReminder) {
              return latest;
            }
            if (!latest) {
              return current;
            }
            return new Date(current.completedDate) > new Date(latest.completedDate) ? current : latest;
          }, undefined)
        : undefined;

    const hasSentReminderRecently = latestManualReminder
      ? subtractDate(new Date(), latestManualReminder.completedDate.getTime(), 'millisecond').getTime() <
        RESEND_INTERVALS
      : false;

    if (hasSentReminderRecently) {
      throw new BadRequestError(OnboardingEmailMessage.CooldownPeriodActive);
    }
  }

  public async sendManualReminder({ onboardingId, initiativeId }: { onboardingId: ObjectId; initiativeId: ObjectId }) {
    const onboarding = await Onboarding.findOne({
      _id: onboardingId,
      $or: [
        { initiativeId },
        { 'user.permissions.initiativeId': initiativeId },
      ],
    })
      .orFail()
      .exec();
    onboarding.notifications = onboarding.notifications ?? { items: [], service: ObNotificationService.Local };
    await this.ensureAllowedToResend(onboarding);
    return this.handleFollowUpEmail(
      onboarding as OnboardingWithNotifications,
      ObNotificationCode.ManualReminder
    );
  }
}

export const createOnboardingEmailService = () => {
  return new OnboardingEmailService(
    createMailService(),
    getDomainConfigRepository(),
    getAppConfigService(),
    getEmailTransactionService(),
    getRootInitiativeService(),
  );
};

let instance: OnboardingEmailService;
export const getOnboardingEmailService = () => {
  if (!instance) {
    instance = createOnboardingEmailService();
  }
  return instance;
}
