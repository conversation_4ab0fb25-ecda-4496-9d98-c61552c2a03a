/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import express from 'express';
import { type AuthRouter } from '../../http/AuthRouter';
import ReportDocument from '../../models/reportDocument';
import { canManageCustomReports } from '../../middleware/initiativeMiddlewares';
import { mustValidate } from '../../util/validation';
import { createReportDocumentSchema, updateReportDocumentSchema } from '../validation-schemas/report-document';
import { getReportDocumentManager } from '../../service/report-document/ReportDocumentManager';
import { ObjectId } from 'bson';
import { checkIsStaff } from '../../middleware/userMiddlewares';
import ContextError from '../../error/ContextError';

const router = express.Router({ mergeParams: true }) as AuthRouter;
const reportDocumentManager = getReportDocumentManager();

router
  .route('/')
  .get(canManageCustomReports, (req, res, next) => {
    const initiativeId = req.params.initiativeId;
    ReportDocument.find({ initiativeId })
      .lean()
      .exec()
      .then((documents) => {
        res.FromModel(documents);
      })
      .catch((err) => {
        next(err);
      });
  })
  .post(canManageCustomReports, async (req, res, next) => {
    try {
      const { surveyId, ...createData } = mustValidate(req.body, createReportDocumentSchema);
      const doc = await reportDocumentManager.create({
        ...createData,
        surveyIds: surveyId ? [surveyId] : undefined,
        initiativeId: new ObjectId(req.params.initiativeId),
        createdBy: req.user._id,
      });
      res.FromModel(doc);
    } catch (err) {
      next(err);
    }
  });

router
  .route('/:reportId')
  .get(canManageCustomReports, (req, res, next) => {
    const initiativeId = req.params.initiativeId;
    const reportId = req.params.reportId;

    ReportDocument.findOne({ initiativeId, _id: reportId })
      .lean()
      .orFail()
      .exec()
      .then((reportDocument) => {
        res.FromModel(reportDocument);
      })
      .catch((err) => {
        next(err);
      });
  })
  .put(canManageCustomReports, async (req, res, next) => {
    try {
      const initiativeId = req.params.initiativeId;
      const reportId = req.params.reportId;
      const updateData = mustValidate(req.body, updateReportDocumentSchema);
      const reportDocument = await ReportDocument.findOne({ initiativeId, _id: reportId }).orFail().exec();

      reportDocument.set(updateData);
      const doc = await reportDocument.save();
      res.FromModel(doc);
    } catch (err) {
      next(err);
    }
  })
  .delete(canManageCustomReports, async (req, res, next) => {
    try {
      const report = await reportDocumentManager.deleteReport({
        reportId: req.params.reportId,
        initiativeId: req.params.initiativeId,
      });

      res.FromModel({ _id: report._id, message: 'Report deleted' });
    } catch (err) {
      next(err);
    }
  });

router.route('/:reportId/template').get(checkIsStaff, async (req, res, next) => {
  const result = await reportDocumentManager.getTemplate({
    reportId: req.params.reportId,
    user: req.user,
  });

  res.FromModel(result);
});

router.route('/:reportId/download').post(checkIsStaff, async (req, res, next) => {
  const result = await reportDocumentManager.download({
    reportId: req.params.reportId,
    editorState: req.body.editorState,
  });

  if (!result) {
    next(new ContextError('No report document found!', { reportId: req.params.reportId }));
    return;
  }

  res.FromModel(result);
});

router.route('/:reportId/initialize-state').get(checkIsStaff, async (req, res, next) => {
  const result = await reportDocumentManager.initializeLexicalStateFromTemplate({
    reportId: req.params.reportId,
    user: req.user,
  });

  res.FromModel(result);
});

module.exports = router;
