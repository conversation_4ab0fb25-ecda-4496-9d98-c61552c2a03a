/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const sdg_6_3_pc_waste_water_treated: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/6.3/pc-waste-water-treated',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/6.3/pc-waste-water-treated',
  fragmentUtrCodes: [
    'gri/2019/303-4/a',
    'gri/2019/303-4/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-4/a': vc.allValueChain,
    'gri/2019/303-4/b': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-4/a',
        valueListCode: 'third_party_water'
      },
      b: {
        code: 'gri/2019/303-4/b',
        valueListCode: 'other_water'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100*{b}/{a})'
            },
          ],
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                gt: 0
              },
            },
          ]
        },
      ]
    },
  }
};
